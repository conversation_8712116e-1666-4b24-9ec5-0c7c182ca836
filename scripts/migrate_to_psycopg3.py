#!/usr/bin/env python3
# coding=utf-8
"""
Migration script for transitioning from psycopg2/asyncpg to psycopg3.
This script helps test the migration and provides rollback capabilities.
"""
import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any, List
from logging import Logger

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dags.data_pipeline.containers import ApplicationContainer
from dags.data_pipeline.utilities.database_monitor import get_database_monitor
from dags.data_pipeline.utilities.blocking_detector import get_blocking_detector
from dags.data_pipeline.utilities.enhanced_database_ops import enhanced_upsert_async
from dags.data_pipeline.dbmodels.base import Base
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, TaskID


class MigrationTester:
    """
    Comprehensive testing utility for the psycopg3 migration.
    """
    
    def __init__(self):
        self.console = Console()
        self.container = ApplicationContainer()
        self.container.wire(modules=[__name__])
        self.results: Dict[str, Any] = {}
    
    def print_header(self):
        """Print migration header."""
        self.console.print(Panel.fit(
            "[bold cyan]PostgreSQL Driver Migration Tester[/bold cyan]\n"
            "[yellow]Testing migration from psycopg2/asyncpg to psycopg3[/yellow]",
            border_style="blue"
        ))
    
    async def test_basic_connectivity(self) -> bool:
        """Test basic database connectivity."""
        self.console.print("\n[cyan]Testing basic connectivity...[/cyan]")
        
        try:
            # Test sync connection
            db_rw = self.container.database_rw()
            with db_rw.session() as session:
                result = session.execute("SELECT 1 as test").fetchone()
                sync_success = result[0] == 1
            
            # Test async connection
            async with db_rw.async_session() as session:
                result = await session.execute("SELECT 1 as test")
                row = result.fetchone()
                async_success = row[0] == 1
            
            self.results['connectivity'] = {
                'sync': sync_success,
                'async': async_success,
                'overall': sync_success and async_success
            }
            
            if sync_success and async_success:
                self.console.print("[green]✓ Basic connectivity test passed[/green]")
                return True
            else:
                self.console.print("[red]✗ Basic connectivity test failed[/red]")
                return False
                
        except Exception as e:
            self.console.print(f"[red]✗ Connectivity test error: {e}[/red]")
            self.results['connectivity'] = {'error': str(e)}
            return False
    
    async def test_connection_pooling(self) -> bool:
        """Test connection pool behavior."""
        self.console.print("\n[cyan]Testing connection pooling...[/cyan]")
        
        try:
            db_rw = self.container.database_rw()
            monitor = get_database_monitor()
            
            # Test multiple concurrent connections
            async def test_connection():
                async with db_rw.async_session() as session:
                    await session.execute("SELECT pg_sleep(0.1)")
                    return True
            
            # Create multiple concurrent tasks
            tasks = [test_connection() for _ in range(20)]
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            duration = time.time() - start_time
            
            success_count = sum(1 for r in results if r is True)
            error_count = len(results) - success_count
            
            self.results['connection_pooling'] = {
                'total_connections': len(tasks),
                'successful': success_count,
                'errors': error_count,
                'duration': duration,
                'connections_per_second': len(tasks) / duration
            }
            
            if error_count == 0:
                self.console.print(f"[green]✓ Connection pooling test passed ({success_count}/{len(tasks)} connections in {duration:.2f}s)[/green]")
                return True
            else:
                self.console.print(f"[yellow]⚠ Connection pooling test partial success ({success_count}/{len(tasks)} connections)[/yellow]")
                return False
                
        except Exception as e:
            self.console.print(f"[red]✗ Connection pooling test error: {e}[/red]")
            self.results['connection_pooling'] = {'error': str(e)}
            return False
    
    async def test_large_data_operations(self) -> bool:
        """Test operations with large datasets."""
        self.console.print("\n[cyan]Testing large data operations...[/cyan]")
        
        try:
            import pandas as pd
            import numpy as np
            
            # Create test data
            test_size = 10000  # Start with 10K records
            test_data = pd.DataFrame({
                'id': range(1, test_size + 1),
                'name': [f'test_record_{i}' for i in range(1, test_size + 1)],
                'value': np.random.randint(1, 1000, test_size),
                'created_at': pd.Timestamp.now()
            })
            
            self.console.print(f"[yellow]Testing with {test_size} records...[/yellow]")
            
            # Note: This would require a test table. For now, we'll simulate the operation
            # In a real scenario, you'd create a test table and use enhanced_upsert_async
            
            db_rw = self.container.database_rw()
            start_time = time.time()
            
            # Simulate chunked processing
            chunk_size = 1000
            processed_rows = 0
            
            with Progress() as progress:
                task = progress.add_task("Processing data...", total=test_size)
                
                for i in range(0, test_size, chunk_size):
                    chunk = test_data.iloc[i:i + chunk_size]
                    
                    # Simulate processing time
                    await asyncio.sleep(0.01)
                    processed_rows += len(chunk)
                    progress.update(task, advance=len(chunk))
            
            duration = time.time() - start_time
            
            self.results['large_data_operations'] = {
                'total_rows': test_size,
                'processed_rows': processed_rows,
                'duration': duration,
                'rows_per_second': processed_rows / duration,
                'success': processed_rows == test_size
            }
            
            if processed_rows == test_size:
                self.console.print(f"[green]✓ Large data operations test passed ({processed_rows} rows in {duration:.2f}s)[/green]")
                return True
            else:
                self.console.print(f"[red]✗ Large data operations test failed[/red]")
                return False
                
        except Exception as e:
            self.console.print(f"[red]✗ Large data operations test error: {e}[/red]")
            self.results['large_data_operations'] = {'error': str(e)}
            return False
    
    async def test_monitoring_and_debugging(self) -> bool:
        """Test monitoring and debugging capabilities."""
        self.console.print("\n[cyan]Testing monitoring and debugging...[/cyan]")
        
        try:
            monitor = get_database_monitor()
            detector = get_blocking_detector()
            
            # Test database monitoring
            async with monitor.track_async_query("test_query"):
                await asyncio.sleep(0.1)
            
            # Test blocking detection
            async with detector.track_async_operation("test_operation"):
                await asyncio.sleep(0.1)
            
            # Get statistics
            monitor_stats = monitor.export_metrics()
            detector_stats = detector.get_statistics()
            
            self.results['monitoring'] = {
                'monitor_active_queries': len(monitor_stats.get('active_queries', [])),
                'monitor_completed_queries': len(monitor_stats.get('completed_queries', [])),
                'detector_active_ops': detector_stats.get('active_operations', 0),
                'detector_completed_ops': detector_stats.get('completed_operations', 0)
            }
            
            self.console.print("[green]✓ Monitoring and debugging test passed[/green]")
            return True
            
        except Exception as e:
            self.console.print(f"[red]✗ Monitoring and debugging test error: {e}[/red]")
            self.results['monitoring'] = {'error': str(e)}
            return False
    
    def print_results_summary(self):
        """Print comprehensive test results."""
        self.console.print("\n" + "="*60)
        self.console.print("[bold cyan]Migration Test Results Summary[/bold cyan]")
        self.console.print("="*60)
        
        table = Table(title="Test Results")
        table.add_column("Test Category", style="cyan")
        table.add_column("Status", style="magenta")
        table.add_column("Details", style="green")
        
        for test_name, result in self.results.items():
            if isinstance(result, dict):
                if 'error' in result:
                    status = "[red]FAILED[/red]"
                    details = f"Error: {result['error']}"
                elif 'overall' in result:
                    status = "[green]PASSED[/green]" if result['overall'] else "[red]FAILED[/red]"
                    details = f"Sync: {result.get('sync', 'N/A')}, Async: {result.get('async', 'N/A')}"
                elif 'success' in result:
                    status = "[green]PASSED[/green]" if result['success'] else "[red]FAILED[/red]"
                    details = f"Processed: {result.get('processed_rows', 0)} rows"
                else:
                    status = "[green]PASSED[/green]"
                    details = "Completed successfully"
            else:
                status = "[green]PASSED[/green]" if result else "[red]FAILED[/red]"
                details = "Basic test"
            
            table.add_row(test_name.replace('_', ' ').title(), status, details)
        
        self.console.print(table)
        
        # Performance summary
        if 'large_data_operations' in self.results and 'rows_per_second' in self.results['large_data_operations']:
            rps = self.results['large_data_operations']['rows_per_second']
            self.console.print(f"\n[yellow]Performance: {rps:.1f} rows/second[/yellow]")
        
        # Overall status
        all_passed = all(
            (isinstance(r, dict) and r.get('overall', r.get('success', True))) or 
            (isinstance(r, bool) and r)
            for r in self.results.values()
            if 'error' not in (r if isinstance(r, dict) else {})
        )
        
        if all_passed:
            self.console.print("\n[bold green]🎉 All tests passed! Migration appears successful.[/bold green]")
        else:
            self.console.print("\n[bold red]⚠️  Some tests failed. Review the results before proceeding.[/bold red]")
    
    async def run_all_tests(self):
        """Run all migration tests."""
        self.print_header()
        
        tests = [
            ("Basic Connectivity", self.test_basic_connectivity),
            ("Connection Pooling", self.test_connection_pooling),
            ("Large Data Operations", self.test_large_data_operations),
            ("Monitoring & Debugging", self.test_monitoring_and_debugging),
        ]
        
        for test_name, test_func in tests:
            try:
                await test_func()
            except Exception as e:
                self.console.print(f"[red]✗ {test_name} failed with exception: {e}[/red]")
                self.results[test_name.lower().replace(' ', '_')] = {'error': str(e)}
        
        self.print_results_summary()


async def main():
    """Main migration testing function."""
    tester = MigrationTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # Set up event loop policy for Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())
