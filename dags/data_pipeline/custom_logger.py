# coding=utf-8
import asyncio
import logging
from logging.handlers import TimedRotatingFileHandler, QueueListener
from datetime import datetime
from zoneinfo import ZoneInfo
from rich.logging import RichHandler

class MaskAuthorizationFilter(logging.Filter):
    def filter(self, record):
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            try:
                msg_dict = eval(record.msg)
                if isinstance(msg_dict, dict) and 'headers' in msg_dict and 'Authorization' in msg_dict['headers']:
                    msg_dict['headers']['Authorization'] = '***MASKED***'
                    record.msg = str(msg_dict)
            except (SyntaxError, NameError) as e:
                pass  # If it's not a valid dictionary string, do nothing
        return True

class AutoStartQueueListener(QueueListener):

    def __init__(self, queue, *handlers, respect_handler_level=False):
        super().__init__(queue, *handlers, respect_handler_level=respect_handler_level)
        # Start the listener immediately.
        self.start()

    def stop_listener(self):
        self.stop()

class TaskNameFilter(logging.Filter):
    def filter(self, record):
        if hasattr(record, "task_name"):
            # If task_name is already set, don't override
            return True

        try:
            loop = asyncio.get_running_loop()
            task = asyncio.current_task(loop=loop)
            record.task_name = task.get_name() if task else 'NoTask'
        except RuntimeError as e:

            record.task_name = "NoEventLoop"
        return True

class CustomFormatter(logging.Formatter):
    """Custom formatter to include timezone-aware timestamps."""

    TZ_MAP = {"India Standard Time": "Asia/Kolkata"}

    def formatTime(self, record, datefmt=None):
        local_time = datetime.now().astimezone()
        timezone_str = local_time.strftime('%Z')
        local_timezone = ZoneInfo(self.TZ_MAP.get(timezone_str, timezone_str))
        dt = datetime.fromtimestamp(record.created, local_timezone)
        return dt.strftime(datefmt or "%Y-%m-%d %H:%M:%S %Z")


class CustomConsoleHandler(RichHandler):
    def emit(self, record):
        # Only emit logs if the level is WARNING or higher
        if record.levelno >= logging.WARNING:
            try:
                formatted_record = self.format(record)
                self.console.print(formatted_record)  # Use Rich's console to print
            except Exception as e:
                self.handleError(record)
                raise e


class SafeTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Custom file handler to handle file permission errors during log rotation."""

    def doRollover(self):
        retries = 5
        while retries > 0:
            try:
                super().doRollover()
                break
            except PermissionError:
                retries -= 1
                if retries == 0:
                    raise


# class CustomLogger:
#     """Custom logger with RichHandler for console and file-based logging."""
#
#     def __init__(self, name, log_file, use_formatter=True):
#         self.logger = logging.getLogger(name)
#         self.logger.setLevel(logging.DEBUG)
#         self.log_queue = queue.Queue(-1)  # Unlimited size
#
#         # Setup handlers
#         console_handler = self._get_console_handler(use_formatter)
#         file_handler = self._get_file_handler(log_file, use_formatter)
#
#         # Queue handler for thread-safe logging
#         queue_handler = QueueHandler(self.log_queue)
#         self.logger.addHandler(queue_handler)
#
#         # Listener to process logs from the queue
#         self.listener = QueueListener(self.log_queue, console_handler, file_handler)
#         self.listener.start()
#
#         # Stop listener on application exit
#         atexit.register(self.listener.stop)
#         self.logger.propagate = False
#
#     def _get_console_handler(self, use_formatter):
#         """Configure RichHandler for console logs."""
#         console_handler = RichHandler(rich_tracebacks=True, markup=True, stream=sys.stdout)
#         if use_formatter:
#             formatter = CustomFormatter(
#                 "%(asctime)s — %(name)s — %(levelname)s — %(message)s",
#                 datefmt="%Y-%m-%d %H:%M:%S %Z"
#             )
#             console_handler.setFormatter(formatter)
#         return console_handler
#
#     def _get_file_handler(self, log_file, use_formatter):
#         """Configure TimedRotatingFileHandler for file-based logs."""
#         file_handler = SafeTimedRotatingFileHandler(log_file, when="midnight", backupCount=3, delay=True)
#         file_handler.setLevel(logging.DEBUG)
#         if use_formatter:
#             formatter = CustomFormatter(
#                 "%(asctime)s.%(msecs)03d %(levelname)-8s %(funcName)-31s %(lineno)-4d | %(message)s",
#                 datefmt="%Y-%m-%dT%H:%M:%S"
#             )
#             file_handler.setFormatter(formatter)
#         return file_handler
#
#     def get_logger(self):
#         """Return the configured logger."""
#         return self.logger


# Usage example
# if __name__ == "__main__":
#     log_file_name = os.getenv("AIRFLOW_HOME", "/tmp") + "/main.log"
#     custom_logger = CustomLogger(__name__, log_file_name, use_formatter=True)
#     logger = custom_logger.get_logger()
#
#     logger.info("This is an info message.")
#     logger.error("This is an error message.")
