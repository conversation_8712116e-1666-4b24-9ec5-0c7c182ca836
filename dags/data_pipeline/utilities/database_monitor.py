# coding=utf-8
"""
Database monitoring and debugging utilities for tracking performance issues,
connection pool health, and identifying blocking operations.
"""
import asyncio
import time
import threading
import psutil
import traceback
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from logging import Logger

from dependency_injector.wiring import inject, Provide
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.pool import Pool
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel

from dags.data_pipeline.containers import LoggerContainer


@dataclass
class QueryMetrics:
    """Metrics for tracking database query performance."""
    query_id: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    query_text: str = ""
    parameters: Optional[Dict] = None
    row_count: Optional[int] = None
    error: Optional[str] = None
    stack_trace: Optional[str] = None
    memory_before: Optional[float] = None
    memory_after: Optional[float] = None
    connection_pool_stats: Optional[Dict] = None


@dataclass
class ConnectionPoolStats:
    """Statistics for database connection pool monitoring."""
    pool_size: int
    checked_in: int
    checked_out: int
    overflow: int
    invalid: int
    timestamp: datetime = field(default_factory=datetime.now)


class DatabaseMonitor:
    """
    Comprehensive database monitoring utility for tracking performance,
    connection pool health, and identifying blocking operations.
    """
    
    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger
        self.active_queries: Dict[str, QueryMetrics] = {}
        self.completed_queries: List[QueryMetrics] = []
        self.pool_stats_history: List[ConnectionPoolStats] = []
        self.monitoring_active = False
        self.console = Console()
        self._lock = threading.Lock()
        
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def get_pool_stats(self, engine: Engine) -> ConnectionPoolStats:
        """Extract connection pool statistics from SQLAlchemy engine."""
        pool = engine.pool
        return ConnectionPoolStats(
            pool_size=pool.size(),
            checked_in=pool.checkedin(),
            checked_out=pool.checkedout(),
            overflow=pool.overflow(),
            invalid=pool.invalid()
        )
    
    @contextmanager
    def track_query(self, query_text: str = "", parameters: Optional[Dict] = None):
        """Context manager for tracking synchronous database queries."""
        query_id = f"sync_{int(time.time() * 1000000)}"
        
        metrics = QueryMetrics(
            query_id=query_id,
            start_time=time.time(),
            query_text=query_text,
            parameters=parameters,
            memory_before=self.get_memory_usage(),
            stack_trace=traceback.format_stack()
        )
        
        with self._lock:
            self.active_queries[query_id] = metrics
        
        try:
            yield metrics
        except Exception as e:
            metrics.error = str(e)
            if self.logger:
                self.logger.error(f"Query {query_id} failed: {e}")
            raise
        finally:
            metrics.end_time = time.time()
            metrics.duration = metrics.end_time - metrics.start_time
            metrics.memory_after = self.get_memory_usage()
            
            with self._lock:
                self.active_queries.pop(query_id, None)
                self.completed_queries.append(metrics)
                
                # Keep only last 1000 completed queries
                if len(self.completed_queries) > 1000:
                    self.completed_queries = self.completed_queries[-1000:]
    
    @asynccontextmanager
    async def track_async_query(self, query_text: str = "", parameters: Optional[Dict] = None):
        """Context manager for tracking asynchronous database queries."""
        query_id = f"async_{int(time.time() * 1000000)}"
        
        metrics = QueryMetrics(
            query_id=query_id,
            start_time=time.time(),
            query_text=query_text,
            parameters=parameters,
            memory_before=self.get_memory_usage(),
            stack_trace=traceback.format_stack()
        )
        
        with self._lock:
            self.active_queries[query_id] = metrics
        
        try:
            yield metrics
        except Exception as e:
            metrics.error = str(e)
            if self.logger:
                self.logger.error(f"Async query {query_id} failed: {e}")
            raise
        finally:
            metrics.end_time = time.time()
            metrics.duration = metrics.end_time - metrics.start_time
            metrics.memory_after = self.get_memory_usage()
            
            with self._lock:
                self.active_queries.pop(query_id, None)
                self.completed_queries.append(metrics)
                
                # Keep only last 1000 completed queries
                if len(self.completed_queries) > 1000:
                    self.completed_queries = self.completed_queries[-1000:]
    
    def get_long_running_queries(self, threshold_seconds: float = 30.0) -> List[QueryMetrics]:
        """Get queries that have been running longer than the threshold."""
        current_time = time.time()
        long_running = []
        
        with self._lock:
            for metrics in self.active_queries.values():
                duration = current_time - metrics.start_time
                if duration > threshold_seconds:
                    metrics.duration = duration  # Update duration for display
                    long_running.append(metrics)
        
        return long_running
    
    def get_slow_queries(self, threshold_seconds: float = 5.0) -> List[QueryMetrics]:
        """Get completed queries that took longer than the threshold."""
        return [
            q for q in self.completed_queries 
            if q.duration and q.duration > threshold_seconds
        ]
    
    def create_monitoring_table(self) -> Table:
        """Create a Rich table for displaying monitoring information."""
        table = Table(title="Database Monitoring Dashboard")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="magenta")
        table.add_column("Details", style="green")
        
        # Active queries
        active_count = len(self.active_queries)
        table.add_row("Active Queries", str(active_count), "Currently executing")
        
        # Long running queries
        long_running = self.get_long_running_queries(10.0)
        table.add_row("Long Running (>10s)", str(len(long_running)), 
                     f"Longest: {max([q.duration or 0 for q in long_running], default=0):.2f}s")
        
        # Recent slow queries
        slow_queries = self.get_slow_queries(2.0)
        recent_slow = [q for q in slow_queries if q.end_time and q.end_time > time.time() - 300]
        table.add_row("Recent Slow (>2s)", str(len(recent_slow)), "Last 5 minutes")
        
        # Memory usage
        memory_mb = self.get_memory_usage()
        table.add_row("Memory Usage", f"{memory_mb:.1f} MB", "Current process")
        
        return table
    
    async def start_monitoring(self, interval: float = 5.0, display_live: bool = True):
        """Start continuous monitoring with optional live display."""
        self.monitoring_active = True
        
        if display_live:
            with Live(self.create_monitoring_table(), refresh_per_second=1) as live:
                while self.monitoring_active:
                    await asyncio.sleep(interval)
                    live.update(self.create_monitoring_table())
                    
                    # Log warnings for long-running queries
                    long_running = self.get_long_running_queries(30.0)
                    for query in long_running:
                        if self.logger:
                            self.logger.warning(
                                f"Long-running query detected: {query.query_id} "
                                f"({query.duration:.2f}s) - {query.query_text[:100]}..."
                            )
        else:
            while self.monitoring_active:
                await asyncio.sleep(interval)
                
                # Log monitoring info
                if self.logger:
                    active_count = len(self.active_queries)
                    long_running = self.get_long_running_queries(30.0)
                    memory_mb = self.get_memory_usage()
                    
                    self.logger.info(
                        f"DB Monitor - Active: {active_count}, "
                        f"Long-running: {len(long_running)}, "
                        f"Memory: {memory_mb:.1f}MB"
                    )
    
    def stop_monitoring(self):
        """Stop the monitoring process."""
        self.monitoring_active = False
    
    def export_metrics(self) -> Dict[str, Any]:
        """Export all collected metrics for analysis."""
        return {
            "active_queries": [
                {
                    "query_id": q.query_id,
                    "duration": time.time() - q.start_time,
                    "query_text": q.query_text[:200],
                    "memory_before": q.memory_before
                }
                for q in self.active_queries.values()
            ],
            "completed_queries": [
                {
                    "query_id": q.query_id,
                    "duration": q.duration,
                    "query_text": q.query_text[:200],
                    "error": q.error,
                    "memory_delta": (q.memory_after or 0) - (q.memory_before or 0)
                }
                for q in self.completed_queries[-100:]  # Last 100 queries
            ],
            "pool_stats": [
                {
                    "timestamp": stats.timestamp.isoformat(),
                    "pool_size": stats.pool_size,
                    "checked_in": stats.checked_in,
                    "checked_out": stats.checked_out,
                    "overflow": stats.overflow
                }
                for stats in self.pool_stats_history[-50:]  # Last 50 snapshots
            ]
        }


# Global database monitor instance
db_monitor = DatabaseMonitor()


@inject
def get_database_monitor(logger: Logger = Provide[LoggerContainer.logger]) -> DatabaseMonitor:
    """Get the global database monitor instance with logger."""
    if not db_monitor.logger:
        db_monitor.logger = logger
    return db_monitor


# Decorator for automatic query tracking
def track_database_operation(query_description: str = ""):
    """Decorator to automatically track database operations."""
    def decorator(func: Callable):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                monitor = get_database_monitor()
                async with monitor.track_async_query(query_description):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                monitor = get_database_monitor()
                with monitor.track_query(query_description):
                    return func(*args, **kwargs)
            return sync_wrapper
    return decorator
