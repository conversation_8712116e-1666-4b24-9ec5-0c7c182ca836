# coding=utf-8
"""
Enhanced database operations with improved chunking, monitoring, and streaming
capabilities for handling large datasets efficiently.
"""
import asyncio
import time
import pandas as pd
import numpy as np
from typing import Type, Optional, Generator, AsyncGenerator, Dict, Any, List
from itertools import islice
from logging import Logger

from dependency_injector.wiring import inject, Provide
from sqlalchemy import inspect, text
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import BinaryExpression, BooleanClauseList

from dags.data_pipeline.containers import LoggerContainer
from dags.data_pipeline.dbmodels.base import Base
from dags.data_pipeline.utilities.database_monitor import get_database_monitor, track_database_operation


class EnhancedChunker:
    """
    Enhanced chunking utility for processing large datasets with adaptive batch sizing
    and memory monitoring.
    """
    
    def __init__(self, initial_batch_size: int = 1000, max_memory_mb: float = 500.0):
        self.initial_batch_size = initial_batch_size
        self.max_memory_mb = max_memory_mb
        self.current_batch_size = initial_batch_size
        self.monitor = get_database_monitor()
    
    def adaptive_chunk_rows(self, rows: List[Dict], max_params: int = 65535) -> Generator[List[Dict], None, None]:
        """
        Adaptively chunk rows based on parameter limits and memory usage.
        
        Args:
            rows: List of row dictionaries to chunk
            max_params: Maximum number of parameters per query (PostgreSQL limit)
            
        Yields:
            Chunks of rows that fit within parameter and memory constraints
        """
        if not rows:
            return
            
        num_columns = len(rows[0]) if rows else 0
        if num_columns == 0:
            return
            
        # Calculate max rows based on PostgreSQL parameter limit
        max_rows_per_batch = max_params // num_columns if num_columns else 1
        
        # Start with the smaller of initial batch size or parameter limit
        batch_size = min(self.current_batch_size, max_rows_per_batch)
        
        iterator = iter(rows)
        while True:
            # Get memory usage before processing chunk
            memory_before = self.monitor.get_memory_usage()
            
            chunk = list(islice(iterator, batch_size))
            if not chunk:
                break
                
            yield chunk
            
            # Monitor memory usage and adjust batch size
            memory_after = self.monitor.get_memory_usage()
            memory_delta = memory_after - memory_before
            
            # Adaptive batch size adjustment
            if memory_delta > self.max_memory_mb:
                # Reduce batch size if memory usage is too high
                self.current_batch_size = max(100, int(batch_size * 0.7))
            elif memory_delta < self.max_memory_mb * 0.3:
                # Increase batch size if memory usage is low
                self.current_batch_size = min(max_rows_per_batch, int(batch_size * 1.3))
            
            batch_size = min(self.current_batch_size, max_rows_per_batch)


@track_database_operation("enhanced_upsert_sync")
@inject
def enhanced_upsert(
    session: Session,
    model: Type[Base],
    rows: pd.DataFrame,
    no_update_cols: tuple[str, ...] = (),
    on_conflict_update: bool = True,
    conflict_condition: Optional[List | BinaryExpression | BooleanClauseList] = None,
    batch_size: Optional[int] = None,
    max_memory_mb: float = 500.0,
    progress_callback: Optional[callable] = None,
    my_logger: Logger = Provide[LoggerContainer.logger],
) -> Dict[str, Any]:
    """
    Enhanced upsert operation with adaptive chunking, memory monitoring, and progress tracking.
    
    Args:
        session: SQLAlchemy session
        model: SQLAlchemy model class
        rows: DataFrame containing rows to upsert
        no_update_cols: Columns that should not be updated on conflict
        on_conflict_update: Whether to perform updates on conflict
        conflict_condition: Additional conditions for conflict resolution
        batch_size: Initial batch size (will be adapted based on memory usage)
        max_memory_mb: Maximum memory usage per batch in MB
        progress_callback: Optional callback function for progress updates
        my_logger: Logger instance
        
    Returns:
        Dictionary with operation statistics
    """
    start_time = time.time()
    total_rows = rows.shape[0]
    processed_rows = 0
    
    if total_rows == 0:
        return {"total_rows": 0, "processed_rows": 0, "duration": 0, "batches": 0}
    
    my_logger.info(f"Starting enhanced upsert for {total_rows} rows")
    
    # Prepare data
    rows_copy = rows.copy()
    rows_copy.replace({np.nan: None}, inplace=True)
    
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]
    
    # Build update columns
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]
    
    # Configure conflict handling
    if on_conflict_update and update_cols:
        update_dict = {c: stmt.excluded[c] for c in update_cols}
        if conflict_condition:
            stmt = stmt.on_conflict_do_update(
                index_elements=primary_keys,
                set_=update_dict,
                where=conflict_condition
            )
        else:
            stmt = stmt.on_conflict_do_update(
                index_elements=primary_keys,
                set_=update_dict
            )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)
    
    # Convert to list of dictionaries
    processed_data = rows_copy.to_dict('records')
    
    # Handle foreign key constraints
    foreign_keys = {
        c.name: list(c.foreign_keys)[0].column
        for c in table.columns if c.foreign_keys
    }
    
    if foreign_keys:
        for row in processed_data:
            for c_name, c_value in foreign_keys.items():
                foreign_obj = row.pop(c_value.table.name, None)
                if foreign_obj:
                    row[c_name] = getattr(foreign_obj, c_value.name)
                else:
                    row[c_name] = row.get(c_name)
    
    # Initialize chunker
    chunker = EnhancedChunker(
        initial_batch_size=batch_size or 1000,
        max_memory_mb=max_memory_mb
    )
    
    batch_count = 0
    try:
        for batch in chunker.adaptive_chunk_rows(processed_data):
            batch_start = time.time()
            session.execute(stmt, batch)
            batch_duration = time.time() - batch_start
            
            batch_count += 1
            processed_rows += len(batch)
            
            # Progress callback
            if progress_callback:
                progress_callback(processed_rows, total_rows, batch_count, batch_duration)
            
            # Log progress for large operations
            if total_rows > 10000 and batch_count % 10 == 0:
                progress_pct = (processed_rows / total_rows) * 100
                my_logger.info(
                    f"Upsert progress: {processed_rows}/{total_rows} ({progress_pct:.1f}%) "
                    f"- Batch {batch_count}, Current batch size: {chunker.current_batch_size}"
                )
    
    except Exception as e:
        my_logger.error(f"Enhanced upsert failed: {e}", exc_info=True)
        raise
    
    duration = time.time() - start_time
    stats = {
        "total_rows": total_rows,
        "processed_rows": processed_rows,
        "duration": duration,
        "batches": batch_count,
        "avg_batch_size": processed_rows / batch_count if batch_count > 0 else 0,
        "rows_per_second": processed_rows / duration if duration > 0 else 0
    }
    
    my_logger.info(
        f"Enhanced upsert completed: {processed_rows} rows in {duration:.2f}s "
        f"({stats['rows_per_second']:.1f} rows/sec) using {batch_count} batches"
    )
    
    return stats


@track_database_operation("enhanced_upsert_async")
@inject
async def enhanced_upsert_async(
    session: AsyncSession,
    model: Type[Base],
    rows: pd.DataFrame,
    no_update_cols: tuple[str, ...] = (),
    on_conflict_update: bool = True,
    conflict_condition: Optional[List | BinaryExpression | BooleanClauseList] = None,
    batch_size: Optional[int] = None,
    max_memory_mb: float = 500.0,
    progress_callback: Optional[callable] = None,
    yield_control_every: int = 5,  # Yield control every N batches
    my_logger: Logger = Provide[LoggerContainer.logger],
) -> Dict[str, Any]:
    """
    Enhanced async upsert operation with adaptive chunking and cooperative multitasking.
    
    Args:
        session: Async SQLAlchemy session
        model: SQLAlchemy model class
        rows: DataFrame containing rows to upsert
        no_update_cols: Columns that should not be updated on conflict
        on_conflict_update: Whether to perform updates on conflict
        conflict_condition: Additional conditions for conflict resolution
        batch_size: Initial batch size (will be adapted based on memory usage)
        max_memory_mb: Maximum memory usage per batch in MB
        progress_callback: Optional async callback function for progress updates
        yield_control_every: Yield control to event loop every N batches
        my_logger: Logger instance
        
    Returns:
        Dictionary with operation statistics
    """
    start_time = time.time()
    total_rows = rows.shape[0]
    processed_rows = 0
    
    if total_rows == 0:
        return {"total_rows": 0, "processed_rows": 0, "duration": 0, "batches": 0}
    
    my_logger.info(f"Starting enhanced async upsert for {total_rows} rows")
    
    # Prepare data (same as sync version)
    rows_copy = rows.copy()
    rows_copy.replace({np.nan: None}, inplace=True)
    
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]
    
    # Build update columns
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]
    
    # Configure conflict handling
    if on_conflict_update and update_cols:
        update_dict = {c: stmt.excluded[c] for c in update_cols}
        if conflict_condition:
            stmt = stmt.on_conflict_do_update(
                index_elements=primary_keys,
                set_=update_dict,
                where=conflict_condition
            )
        else:
            stmt = stmt.on_conflict_do_update(
                index_elements=primary_keys,
                set_=update_dict
            )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)
    
    # Convert to list of dictionaries
    processed_data = rows_copy.to_dict('records')
    
    # Handle foreign key constraints
    foreign_keys = {
        c.name: list(c.foreign_keys)[0].column
        for c in table.columns if c.foreign_keys
    }
    
    if foreign_keys:
        for row in processed_data:
            for c_name, c_value in foreign_keys.items():
                foreign_obj = row.pop(c_value.table.name, None)
                if foreign_obj:
                    row[c_name] = getattr(foreign_obj, c_value.name)
                else:
                    row[c_name] = row.get(c_name)
    
    # Initialize chunker
    chunker = EnhancedChunker(
        initial_batch_size=batch_size or 1000,
        max_memory_mb=max_memory_mb
    )
    
    batch_count = 0
    try:
        for batch in chunker.adaptive_chunk_rows(processed_data):
            batch_start = time.time()
            await session.execute(stmt, batch)
            batch_duration = time.time() - batch_start
            
            batch_count += 1
            processed_rows += len(batch)
            
            # Progress callback
            if progress_callback:
                if asyncio.iscoroutinefunction(progress_callback):
                    await progress_callback(processed_rows, total_rows, batch_count, batch_duration)
                else:
                    progress_callback(processed_rows, total_rows, batch_count, batch_duration)
            
            # Yield control to event loop periodically
            if batch_count % yield_control_every == 0:
                await asyncio.sleep(0)  # Yield control
            
            # Log progress for large operations
            if total_rows > 10000 and batch_count % 10 == 0:
                progress_pct = (processed_rows / total_rows) * 100
                my_logger.info(
                    f"Async upsert progress: {processed_rows}/{total_rows} ({progress_pct:.1f}%) "
                    f"- Batch {batch_count}, Current batch size: {chunker.current_batch_size}"
                )
    
    except Exception as e:
        my_logger.error(f"Enhanced async upsert failed: {e}", exc_info=True)
        raise
    
    duration = time.time() - start_time
    stats = {
        "total_rows": total_rows,
        "processed_rows": processed_rows,
        "duration": duration,
        "batches": batch_count,
        "avg_batch_size": processed_rows / batch_count if batch_count > 0 else 0,
        "rows_per_second": processed_rows / duration if duration > 0 else 0
    }
    
    my_logger.info(
        f"Enhanced async upsert completed: {processed_rows} rows in {duration:.2f}s "
        f"({stats['rows_per_second']:.1f} rows/sec) using {batch_count} batches"
    )
    
    return stats


@track_database_operation("streaming_query")
async def stream_large_query_async(
    session: AsyncSession,
    query: str,
    parameters: Optional[Dict] = None,
    chunk_size: int = 10000,
    my_logger: Logger = None
) -> AsyncGenerator[pd.DataFrame, None]:
    """
    Stream large query results in chunks to avoid memory issues.
    
    Args:
        session: Async SQLAlchemy session
        query: SQL query to execute
        parameters: Query parameters
        chunk_size: Number of rows per chunk
        my_logger: Logger instance
        
    Yields:
        DataFrames containing chunks of query results
    """
    if my_logger:
        my_logger.info(f"Starting streaming query with chunk size {chunk_size}")
    
    # Use server-side cursor for large result sets
    result = await session.execute(text(query), parameters or {})
    
    total_rows = 0
    chunk_count = 0
    
    while True:
        # Fetch chunk
        rows = result.fetchmany(chunk_size)
        if not rows:
            break
            
        # Convert to DataFrame
        df = pd.DataFrame(rows)
        total_rows += len(df)
        chunk_count += 1
        
        if my_logger and chunk_count % 10 == 0:
            my_logger.info(f"Streamed {total_rows} rows in {chunk_count} chunks")
        
        yield df
    
    if my_logger:
        my_logger.info(f"Streaming completed: {total_rows} total rows in {chunk_count} chunks")
