# coding=utf-8
"""
Blocking detection and debugging utilities for identifying where code gets stuck
during large data operations.
"""
import asyncio
import threading
import time
import traceback
import signal
import sys
import psutil
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from contextlib import contextmanager, asynccontextmanager
from logging import Logger

from dependency_injector.wiring import inject, Provide
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live

from dags.data_pipeline.containers import LoggerContainer


@dataclass
class BlockingEvent:
    """Information about a potential blocking event."""
    thread_id: int
    thread_name: str
    function_name: str
    start_time: float
    stack_trace: List[str]
    is_async: bool = False
    task_name: Optional[str] = None
    duration: Optional[float] = None
    status: str = "running"  # running, completed, timeout, error


class BlockingDetector:
    """
    Detects and reports on potentially blocking operations in both sync and async code.
    """
    
    def __init__(self, 
                 timeout_threshold: float = 30.0,
                 check_interval: float = 5.0,
                 logger: Optional[Logger] = None):
        self.timeout_threshold = timeout_threshold
        self.check_interval = check_interval
        self.logger = logger
        self.console = Console()
        
        # Tracking data
        self.active_operations: Dict[str, BlockingEvent] = {}
        self.completed_operations: List[BlockingEvent] = []
        self.monitoring_active = False
        self._lock = threading.Lock()
        
        # Signal handlers for emergency debugging
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for emergency stack trace dumping."""
        def dump_all_stacks(signum, frame):
            self.console.print("\n[red]EMERGENCY STACK DUMP - All Threads:[/red]")
            self.dump_all_thread_stacks()
            
        def dump_blocking_operations(signum, frame):
            self.console.print("\n[yellow]BLOCKING OPERATIONS DUMP:[/yellow]")
            self.report_blocking_operations()
        
        # SIGUSR1 for full stack dump (Linux/Mac)
        if hasattr(signal, 'SIGUSR1'):
            signal.signal(signal.SIGUSR1, dump_all_stacks)
        
        # SIGUSR2 for blocking operations dump (Linux/Mac)
        if hasattr(signal, 'SIGUSR2'):
            signal.signal(signal.SIGUSR2, dump_blocking_operations)
    
    @contextmanager
    def track_operation(self, operation_name: str, function_name: str = ""):
        """Context manager for tracking potentially blocking sync operations."""
        thread_id = threading.get_ident()
        thread_name = threading.current_thread().name
        operation_id = f"sync_{thread_id}_{int(time.time() * 1000000)}"
        
        event = BlockingEvent(
            thread_id=thread_id,
            thread_name=thread_name,
            function_name=function_name or operation_name,
            start_time=time.time(),
            stack_trace=traceback.format_stack(),
            is_async=False
        )
        
        with self._lock:
            self.active_operations[operation_id] = event
        
        try:
            yield event
            event.status = "completed"
        except Exception as e:
            event.status = f"error: {str(e)}"
            raise
        finally:
            event.duration = time.time() - event.start_time
            with self._lock:
                self.active_operations.pop(operation_id, None)
                self.completed_operations.append(event)
                
                # Keep only last 100 completed operations
                if len(self.completed_operations) > 100:
                    self.completed_operations = self.completed_operations[-100:]
    
    @asynccontextmanager
    async def track_async_operation(self, operation_name: str, function_name: str = ""):
        """Context manager for tracking potentially blocking async operations."""
        try:
            task = asyncio.current_task()
            task_name = task.get_name() if task else "unknown"
        except RuntimeError:
            task_name = "no_event_loop"
        
        operation_id = f"async_{task_name}_{int(time.time() * 1000000)}"
        
        event = BlockingEvent(
            thread_id=threading.get_ident(),
            thread_name=threading.current_thread().name,
            function_name=function_name or operation_name,
            start_time=time.time(),
            stack_trace=traceback.format_stack(),
            is_async=True,
            task_name=task_name
        )
        
        with self._lock:
            self.active_operations[operation_id] = event
        
        try:
            yield event
            event.status = "completed"
        except asyncio.CancelledError:
            event.status = "cancelled"
            raise
        except Exception as e:
            event.status = f"error: {str(e)}"
            raise
        finally:
            event.duration = time.time() - event.start_time
            with self._lock:
                self.active_operations.pop(operation_id, None)
                self.completed_operations.append(event)
                
                # Keep only last 100 completed operations
                if len(self.completed_operations) > 100:
                    self.completed_operations = self.completed_operations[-100:]
    
    def get_blocking_operations(self) -> List[BlockingEvent]:
        """Get operations that have been running longer than the threshold."""
        current_time = time.time()
        blocking_ops = []
        
        with self._lock:
            for event in self.active_operations.values():
                duration = current_time - event.start_time
                if duration > self.timeout_threshold:
                    event.duration = duration
                    blocking_ops.append(event)
        
        return blocking_ops
    
    def dump_all_thread_stacks(self):
        """Dump stack traces for all threads."""
        for thread_id, frame in sys._current_frames().items():
            thread = None
            for t in threading.enumerate():
                if t.ident == thread_id:
                    thread = t
                    break
            
            thread_name = thread.name if thread else f"Thread-{thread_id}"
            self.console.print(f"\n[cyan]Thread {thread_name} (ID: {thread_id}):[/cyan]")
            
            stack_lines = traceback.format_stack(frame)
            for line in stack_lines[-10:]:  # Last 10 stack frames
                self.console.print(f"  {line.strip()}")
    
    def report_blocking_operations(self):
        """Report current blocking operations."""
        blocking_ops = self.get_blocking_operations()
        
        if not blocking_ops:
            self.console.print("[green]No blocking operations detected[/green]")
            return
        
        table = Table(title="Blocking Operations Detected")
        table.add_column("Type", style="cyan")
        table.add_column("Function", style="magenta")
        table.add_column("Duration (s)", style="red")
        table.add_column("Thread/Task", style="yellow")
        table.add_column("Status", style="green")
        
        for op in blocking_ops:
            op_type = "Async" if op.is_async else "Sync"
            thread_info = op.task_name if op.is_async else op.thread_name
            
            table.add_row(
                op_type,
                op.function_name,
                f"{op.duration:.2f}",
                thread_info,
                op.status
            )
        
        self.console.print(table)
        
        # Print stack traces for the most problematic operations
        for op in blocking_ops[:3]:  # Top 3 longest running
            self.console.print(f"\n[red]Stack trace for {op.function_name}:[/red]")
            for line in op.stack_trace[-5:]:  # Last 5 frames
                self.console.print(f"  {line.strip()}")
    
    async def start_monitoring(self, display_live: bool = True):
        """Start continuous monitoring for blocking operations."""
        self.monitoring_active = True
        
        if display_live:
            with Live(self._create_monitoring_panel(), refresh_per_second=0.5) as live:
                while self.monitoring_active:
                    await asyncio.sleep(self.check_interval)
                    live.update(self._create_monitoring_panel())
                    
                    # Log warnings for blocking operations
                    blocking_ops = self.get_blocking_operations()
                    for op in blocking_ops:
                        if self.logger:
                            self.logger.warning(
                                f"Blocking operation detected: {op.function_name} "
                                f"running for {op.duration:.2f}s in {op.thread_name}"
                            )
        else:
            while self.monitoring_active:
                await asyncio.sleep(self.check_interval)
                blocking_ops = self.get_blocking_operations()
                
                if blocking_ops and self.logger:
                    self.logger.warning(f"Detected {len(blocking_ops)} blocking operations")
                    for op in blocking_ops:
                        self.logger.warning(
                            f"  - {op.function_name}: {op.duration:.2f}s"
                        )
    
    def _create_monitoring_panel(self) -> Panel:
        """Create a Rich panel for monitoring display."""
        # System info
        process = psutil.Process()
        cpu_percent = process.cpu_percent()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        # Operation counts
        active_count = len(self.active_operations)
        blocking_count = len(self.get_blocking_operations())
        
        content = f"""
[cyan]System Status:[/cyan]
  CPU: {cpu_percent:.1f}%
  Memory: {memory_mb:.1f} MB
  
[yellow]Operations:[/yellow]
  Active: {active_count}
  Blocking (>{self.timeout_threshold}s): {blocking_count}
  
[green]Monitoring:[/green]
  Threshold: {self.timeout_threshold}s
  Check Interval: {self.check_interval}s
        """.strip()
        
        return Panel(content, title="Blocking Detector", border_style="blue")
    
    def stop_monitoring(self):
        """Stop the monitoring process."""
        self.monitoring_active = False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about operations."""
        with self._lock:
            completed_durations = [op.duration for op in self.completed_operations if op.duration]
            
            stats = {
                "active_operations": len(self.active_operations),
                "completed_operations": len(self.completed_operations),
                "blocking_operations": len(self.get_blocking_operations()),
                "avg_duration": sum(completed_durations) / len(completed_durations) if completed_durations else 0,
                "max_duration": max(completed_durations) if completed_durations else 0,
                "error_count": len([op for op in self.completed_operations if op.status.startswith("error")]),
                "cancelled_count": len([op for op in self.completed_operations if op.status == "cancelled"])
            }
        
        return stats


# Global blocking detector instance
blocking_detector = BlockingDetector()


@inject
def get_blocking_detector(logger: Logger = Provide[LoggerContainer.logger]) -> BlockingDetector:
    """Get the global blocking detector instance with logger."""
    if not blocking_detector.logger:
        blocking_detector.logger = logger
    return blocking_detector


# Decorator for automatic blocking detection
def detect_blocking(operation_name: str = ""):
    """Decorator to automatically detect blocking operations."""
    def decorator(func: Callable):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                detector = get_blocking_detector()
                async with detector.track_async_operation(operation_name or func.__name__, func.__name__):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                detector = get_blocking_detector()
                with detector.track_operation(operation_name or func.__name__, func.__name__):
                    return func(*args, **kwargs)
            return sync_wrapper
    return decorator


# Emergency debugging functions
def emergency_stack_dump():
    """Emergency function to dump all thread stacks."""
    detector = get_blocking_detector()
    detector.dump_all_thread_stacks()


def emergency_blocking_report():
    """Emergency function to report blocking operations."""
    detector = get_blocking_detector()
    detector.report_blocking_operations()


# Usage instructions
def print_usage_instructions():
    """Print instructions for using the blocking detector."""
    console = Console()
    console.print("""
[bold cyan]Blocking Detector Usage Instructions:[/bold cyan]

[yellow]1. Automatic Detection (Decorator):[/yellow]
   @detect_blocking("my_operation")
   def my_function():
       # Your code here
       pass

[yellow]2. Manual Tracking:[/yellow]
   detector = get_blocking_detector()
   with detector.track_operation("operation_name"):
       # Your code here
       pass

[yellow]3. Emergency Debugging:[/yellow]
   - Call emergency_stack_dump() to see all thread stacks
   - Call emergency_blocking_report() to see blocking operations
   - Send SIGUSR1 signal for stack dump (Linux/Mac)
   - Send SIGUSR2 signal for blocking report (Linux/Mac)

[yellow]4. Start Monitoring:[/yellow]
   await detector.start_monitoring(display_live=True)

[green]The detector will automatically log warnings for operations
running longer than the threshold (default: 30 seconds).[/green]
    """)
