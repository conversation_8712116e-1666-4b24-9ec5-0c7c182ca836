[project]
name = "container-tests"
version = "0.1.0"
description = "Test suite for containers.py with database driver compatibility"
requires-python = ">=3.8"

dependencies = [
    # Core testing framework
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    
    # Coverage reporting
    "pytest-cov>=4.0.0",
    "coverage[toml]>=7.0.0",
    
    # Allure reporting
    "allure-pytest>=2.12.0",
    "allure-python-commons>=2.12.0",
    
    # Additional testing utilities
    "pytest-xdist>=3.0.0",  # Parallel test execution
    "pytest-timeout>=2.1.0",  # Test timeouts
    "pytest-benchmark>=4.0.0",  # Performance benchmarking
    "pytest-html>=3.1.0",  # HTML test reports
    
    # Mocking and fixtures
    "responses>=0.23.0",  # HTTP mocking
    "freezegun>=1.2.0",  # Time mocking
    "factory-boy>=3.2.0",  # Test data factories
    
    # Database testing utilities
    "pytest-postgresql>=4.1.0",  # PostgreSQL testing
    "sqlalchemy-utils>=0.40.0",  # Database utilities
    
    # Async testing utilities
    "aioresponses>=0.7.0",  # Async HTTP mocking
    "pytest-aiohttp>=1.0.0",  # aiohttp testing
    
    # Core dependencies (should match main project)
    "sqlalchemy>=2.0.0",
    "dependency-injector>=4.41.0",
    "asyncpg>=0.28.0",  # Always include asyncpg as it doesn't conflict
]

[dependency-groups]
# Legacy database drivers (psycopg2) - recommended for existing codebases
legacy = [
    "psycopg2-binary>=2.9.0",  # PostgreSQL adapter for Python (sync)
]

# Modern database drivers (psycopg3) - for new projects
modern = [
    "psycopg[binary]>=3.1.0",  # Modern PostgreSQL adapter
]

# Test dependencies group
test = [
    # Additional test-only dependencies can go here
    "pytest-sugar>=0.9.0",  # Better test output
    "pytest-clarity>=1.0.0",  # Better assertion output
]

# Development dependencies
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

# Documentation dependencies
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
# Pytest configuration
testpaths = ["."]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Markers
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "slow: Slow running tests",
    "database: Tests requiring database connection",
    "async: Asynchronous tests",
]

# Coverage settings
addopts = [
    "--strict-markers",
    "--strict-config", 
    "--verbose",
    "--tb=short",
    "--cov=dags.data_pipeline.containers",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-fail-under=80",
    "--alluredir=allure-results",
]

# Async test configuration
asyncio_mode = "auto"

# Logging
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# Warnings
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning", 
    "ignore::UserWarning:sqlalchemy.*",
]

# Minimum version
minversion = "6.0"

[tool.coverage.run]
source = ["dags.data_pipeline.containers"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["dags"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false
