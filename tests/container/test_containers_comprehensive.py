# coding=utf-8
"""
Comprehensive test suite for containers.py with allure reporting and coverage.

This test suite covers:
- DatabaseSessionManagerContainer functionality
- ManagedPostgresSessionManager lifecycle
- ApplicationContainer dependency injection
- Database lifecycle management
- Schema switching capabilities
- Error handling and cleanup
"""

import pytest
import allure
import asyncio
import weakref
from unittest.mock import MagicMock, patch, AsyncMock
from contextlib import asynccontextmanager

from dags.data_pipeline.containers import (
    DatabaseSessionManagerContainer,
    ManagedPostgresSessionManager,
    PostgresSessionManager,
    ApplicationContainer,
    DatabaseLifecycleManager,
    EntryDetails,
    EntryDetailsBuilder,
    build_entry_details,
    db_lifecycle_manager,
    database_lifecycle,
    with_database_cleanup
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
@allure.title("Mock Database Entry Details")
def mock_entry_details():
    """Fixture providing mock database entry details."""
    return EntryDetails(
        username="test_user",
        password="test_password",
        url="postgresql://test_user:test_password@localhost:5432/testdb",
        custom_properties={
            "DB_SERVER_NAME": "localhost",
            "DB_SERVER_RW_PORT": 5432,
            "DB_SERVER_RO_PORT": 5433,
            "DB_NAME": "testdb"
        }
    )


@pytest.fixture
@allure.title("Mock KeePass Manager")
def mock_keepass_manager():
    """Fixture providing a mock KeePass manager."""
    mock_manager = MagicMock()
    
    # Mock entry for different schemas
    def mock_find_entries(title, first=True):
        mock_entry = MagicMock()
        mock_entry.username = f"user_{title}"
        mock_entry.password = f"pass_{title}"
        mock_entry.url = f"postgresql://user_{title}@localhost:5432/db"
        mock_entry.custom_properties = {
            "DB_SERVER_NAME": "localhost",
            "DB_SERVER_RW_PORT": 5432,
            "DB_SERVER_RO_PORT": 5433,
            "DB_NAME": "testdb"
        }
        mock_entry.get_custom_property = lambda key: mock_entry.custom_properties.get(key)
        return mock_entry
    
    mock_manager.find_entries.side_effect = mock_find_entries
    return mock_manager


@allure.feature("EntryDetails")
@allure.story("Entry Details Management")
class TestEntryDetails:
    """Test suite for EntryDetails and EntryDetailsBuilder."""
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test EntryDetails creation")
    def test_entry_details_creation(self):
        """Test creating EntryDetails with all fields."""
        entry = EntryDetails(
            username="testuser",
            password="testpass",
            url="postgresql://localhost:5432/db",
            custom_properties={"key": "value"}
        )
        
        assert entry.username == "testuser"
        assert entry.password == "testpass"
        assert entry.url == "postgresql://localhost:5432/db"
        assert entry.custom_properties == {"key": "value"}
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test EntryDetailsBuilder pattern")
    def test_entry_details_builder(self):
        """Test the builder pattern for EntryDetails."""
        builder = EntryDetailsBuilder()
        entry = (builder
                .set_username("builder_user")
                .set_password("builder_pass")
                .set_url("postgresql://builder:5432/db")
                .add_custom_property("env", "test")
                .build())
        
        assert entry.username == "builder_user"
        assert entry.password == "builder_pass"
        assert entry.url == "postgresql://builder:5432/db"
        assert entry.custom_properties["env"] == "test"
    
    @allure.severity(allure.severity_level.MINOR)
    @allure.title("Test EntryDetails string representation")
    def test_entry_details_repr(self):
        """Test string representation hides password."""
        entry = EntryDetails(username="user", password="secret")
        repr_str = repr(entry)
        str_str = str(entry)
        
        assert "secret" not in repr_str
        assert "secret" not in str_str
        assert "****" in repr_str
        assert "****" in str_str


@allure.feature("PostgresSessionManager")
@allure.story("Database Session Management")
class TestPostgresSessionManager:
    """Test suite for PostgresSessionManager."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test PostgresSessionManager initialization")
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.URL.create')
    def test_postgres_session_manager_init(self, mock_url_create, mock_async_engine, mock_engine, mock_entry_details):
        """Test PostgresSessionManager initialization."""
        # Mock URL creation
        mock_url_create.return_value = "mocked://connection/string"

        # Mock engines with proper return values
        mock_sync_engine = MagicMock()
        mock_sync_engine.execution_options.return_value = mock_sync_engine
        mock_engine.return_value = mock_sync_engine

        mock_async_engine_instance = MagicMock()
        mock_async_engine_instance.execution_options.return_value = mock_async_engine_instance
        mock_async_engine.return_value = mock_async_engine_instance

        manager = PostgresSessionManager(mock_entry_details, "test_schema", rw=True)

        assert manager.schema == "test_schema"
        assert manager.entry == mock_entry_details
        assert manager.rw is True
        assert not manager._closed

        # Verify engines were created with correct drivers
        mock_engine.assert_called_once()
        mock_async_engine.assert_called_once()

        # Verify URL creation was called with correct drivers
        assert mock_url_create.call_count == 2  # Once for async, once for sync

        # Check that psycopg2 driver was used for sync engine
        sync_call_args = mock_url_create.call_args_list[1]  # Second call is for sync
        assert sync_call_args[1]['drivername'] == "postgresql+psycopg2"

        # Check that asyncpg driver was used for async engine
        async_call_args = mock_url_create.call_args_list[0]  # First call is for async
        assert async_call_args[1]['drivername'] == "postgresql+asyncpg"
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test schema update functionality")
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.URL.create')
    def test_update_schema(self, mock_url_create, mock_async_engine, mock_engine, mock_entry_details):
        """Test schema update functionality."""
        # Mock URL creation
        mock_url_create.return_value = "mocked://connection/string"

        # Setup mocks with proper chaining
        mock_sync_engine = MagicMock()
        mock_sync_engine_updated = MagicMock()
        mock_sync_engine.execution_options.return_value = mock_sync_engine_updated
        mock_engine.return_value = mock_sync_engine

        mock_async_engine_instance = MagicMock()
        mock_async_engine_updated = MagicMock()
        mock_async_engine_instance.execution_options.return_value = mock_async_engine_updated
        mock_async_engine.return_value = mock_async_engine_instance

        manager = PostgresSessionManager(mock_entry_details, "initial_schema", rw=True)

        # Reset mocks to track update_schema calls specifically
        mock_sync_engine.execution_options.reset_mock()
        mock_async_engine_instance.execution_options.reset_mock()

        # Update schema
        result = manager.update_schema("new_schema")

        assert manager.schema == "new_schema"
        assert result is manager  # Should return self for chaining

        # Verify execution_options was called with new schema
        mock_sync_engine.execution_options.assert_called_with(
            schema_translate_map={None: "new_schema"}
        )
        mock_async_engine_instance.execution_options.assert_called_with(
            schema_translate_map={None: "new_schema"}
        )


@allure.feature("ManagedPostgresSessionManager")
@allure.story("Managed Database Sessions")
class TestManagedPostgresSessionManager:
    """Test suite for ManagedPostgresSessionManager."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test auto-registration with lifecycle manager")
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.URL.create')
    def test_auto_registration(self, mock_url_create, mock_async_engine, mock_engine, mock_entry_details):
        """Test that ManagedPostgresSessionManager auto-registers with lifecycle manager."""
        # Mock URL creation
        mock_url_create.return_value = "mocked://connection/string"

        # Mock engines
        mock_engine.return_value.execution_options.return_value = MagicMock()
        mock_async_engine.return_value.execution_options.return_value = MagicMock()

        initial_count = len(db_lifecycle_manager._session_managers)

        manager = ManagedPostgresSessionManager(mock_entry_details, "test_schema")

        # Verify registration
        assert len(db_lifecycle_manager._session_managers) == initial_count + 1
        assert manager in db_lifecycle_manager._session_managers
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test lifecycle manager cleanup")
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.URL.create')
    async def test_lifecycle_manager_cleanup(self, mock_url_create, mock_async_engine, mock_engine, mock_entry_details):
        """Test lifecycle manager cleanup functionality."""
        # Mock URL creation
        mock_url_create.return_value = "mocked://connection/string"

        # Mock engines
        mock_engine.return_value.execution_options.return_value = MagicMock()
        mock_async_engine.return_value.execution_options.return_value = MagicMock()

        manager = ManagedPostgresSessionManager(mock_entry_details, "test_schema")

        # Mock the aclose method
        manager.aclose = AsyncMock()

        # Test async cleanup
        await db_lifecycle_manager.async_cleanup()

        # Verify cleanup was called
        manager.aclose.assert_called_once()


@allure.feature("DatabaseSessionManagerContainer")
@allure.story("Container Dependency Injection")
class TestDatabaseSessionManagerContainer:
    """Test suite for DatabaseSessionManagerContainer."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test container initialization")
    def test_container_initialization(self):
        """Test DatabaseSessionManagerContainer initialization."""
        container = DatabaseSessionManagerContainer()
        
        # Test default values
        assert container.schema() == "public"
        
        # Test provider existence
        assert hasattr(container, 'pg_rw_entry')
        assert hasattr(container, 'pg_ro_entry')
        assert hasattr(container, 'database_rw')
        assert hasattr(container, 'database_ro')
        assert hasattr(container, 'lifecycle_manager')
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test schema override")
    def test_schema_override(self):
        """Test schema override functionality."""
        container = DatabaseSessionManagerContainer()
        
        # Override schema
        container.schema.override("plat")
        
        assert container.schema() == "plat"
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test database provider resolution")
    @patch('dags.data_pipeline.containers.ManagedPostgresSessionManager')
    def test_database_provider_resolution(self, mock_manager_class, mock_entry_details):
        """Test that database providers resolve correctly."""
        container = DatabaseSessionManagerContainer()
        
        # Override entry providers
        container.pg_rw_entry.override(mock_entry_details)
        container.pg_ro_entry.override(mock_entry_details)
        
        # Test provider resolution
        db_rw = container.database_rw()
        db_ro = container.database_ro()
        
        # Verify ManagedPostgresSessionManager was called
        assert mock_manager_class.call_count == 2
        
        # Verify singleton behavior
        db_rw_2 = container.database_rw()
        assert db_rw is db_rw_2  # Should be same instance (Singleton)


@allure.feature("ApplicationContainer")
@allure.story("Application-wide Dependency Injection")
class TestApplicationContainer:
    """Test suite for ApplicationContainer."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test ApplicationContainer initialization")
    def test_application_container_init(self):
        """Test ApplicationContainer initialization."""
        container = ApplicationContainer()
        
        # Test default schema
        assert container.schema() == "public"
        
        # Test provider existence
        assert hasattr(container, 'keepass_manager')
        assert hasattr(container, 'pg_rw_entry')
        assert hasattr(container, 'pg_ro_entry')
        assert hasattr(container, 'database_rw')
        assert hasattr(container, 'database_rw_managed')
        assert hasattr(container, 'database_ro')
        assert hasattr(container, 'database_ro_managed')
        assert hasattr(container, 'lifecycle_manager')
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test lifecycle manager provider")
    def test_lifecycle_manager_provider(self):
        """Test lifecycle manager provider."""
        container = ApplicationContainer()
        
        lifecycle_manager = container.lifecycle_manager()
        
        assert lifecycle_manager is db_lifecycle_manager
        assert isinstance(lifecycle_manager, DatabaseLifecycleManager)


@allure.feature("Utility Functions")
@allure.story("Helper Functions and Decorators")
class TestUtilityFunctions:
    """Test suite for utility functions and decorators."""
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test build_entry_details function")
    def test_build_entry_details(self, mock_keepass_manager):
        """Test build_entry_details function."""
        entry = build_entry_details(mock_keepass_manager, "test_title")
        
        assert entry.username == "user_test_title"
        assert entry.password == "pass_test_title"
        mock_keepass_manager.find_entries.assert_called_once_with(title="test_title", first=True)
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test database_lifecycle context manager")
    async def test_database_lifecycle_context_manager(self):
        """Test database_lifecycle async context manager."""
        # Mock the cleanup method
        with patch.object(db_lifecycle_manager, 'async_cleanup', new_callable=AsyncMock) as mock_cleanup:
            async with database_lifecycle():
                # Context manager should work
                pass
            
            # Cleanup should be called on exit
            mock_cleanup.assert_called_once()
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test with_database_cleanup decorator")
    async def test_with_database_cleanup_decorator(self):
        """Test with_database_cleanup decorator."""
        cleanup_called = False
        
        # Mock the cleanup method
        async def mock_cleanup():
            nonlocal cleanup_called
            cleanup_called = True
        
        with patch.object(db_lifecycle_manager, 'async_cleanup', side_effect=mock_cleanup):
            @with_database_cleanup
            async def test_async_function():
                return "test_result"
            
            result = await test_async_function()
            
            assert result == "test_result"
            assert cleanup_called
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test with_database_cleanup decorator for sync functions")
    def test_with_database_cleanup_decorator_sync(self):
        """Test with_database_cleanup decorator for synchronous functions."""
        cleanup_called = False
        
        # Mock the cleanup method
        def mock_cleanup():
            nonlocal cleanup_called
            cleanup_called = True
        
        with patch.object(db_lifecycle_manager, '_sync_cleanup', side_effect=mock_cleanup):
            @with_database_cleanup
            def test_sync_function():
                return "sync_result"
            
            result = test_sync_function()
            
            assert result == "sync_result"
            assert cleanup_called


@allure.feature("Error Handling")
@allure.story("Exception Handling and Edge Cases")
class TestErrorHandling:
    """Test suite for error handling and edge cases."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test build_entry_details with missing entry")
    def test_build_entry_details_missing_entry(self, mock_keepass_manager):
        """Test build_entry_details when entry is not found."""
        mock_keepass_manager.find_entries.return_value = None
        
        with pytest.raises(LookupError, match="No entry found with given title"):
            build_entry_details(mock_keepass_manager, "nonexistent_title")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test PostgresSessionManager operations after close")
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.URL.create')
    def test_operations_after_close(self, mock_url_create, mock_async_engine, mock_engine, mock_entry_details):
        """Test that operations fail after manager is closed."""
        # Mock URL creation
        mock_url_create.return_value = "mocked://connection/string"

        # Mock engines
        mock_engine.return_value.execution_options.return_value = MagicMock()
        mock_async_engine.return_value.execution_options.return_value = MagicMock()

        manager = PostgresSessionManager(mock_entry_details, "test_schema")
        manager.close()

        # Operations should raise RuntimeError
        with pytest.raises(RuntimeError, match="PostgresSessionManager has been closed"):
            manager.update_schema("new_schema")

        with pytest.raises(RuntimeError, match="PostgresSessionManager has been closed"):
            with manager.session():
                pass

        with pytest.raises(RuntimeError, match="PostgresSessionManager has been closed"):
            manager.get_schemas()


if __name__ == "__main__":
    # Run tests with allure reporting
    pytest.main([
        __file__,
        "--alluredir=allure-results",
        "--cov=dags.data_pipeline.containers",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "-v"
    ])
