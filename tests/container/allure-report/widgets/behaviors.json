{"total": 7, "items": [{"uid": "1d14c8ecf7b0fbb6662d939b8b8953c3", "name": "PostgresSessionManager", "statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "aedfae7c917c649d5e84ab17847c741b", "name": "ManagedPostgresSessionManager", "statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "0a5e45d8fd7a5cc126497acbd4503f9c", "name": "Utility Functions", "statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "f5cbfe097c38ce6d36a240ecb72ab65b", "name": "Erro<PERSON>", "statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "c367718f5899cf27cb25c1b286144a4a", "name": "DatabaseSessionManagerContainer", "statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "0fa9b6682277ff6d85bf97b7c04a63cc", "name": "ApplicationContainer", "statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "03838136887a03fb82e180b4c308c821", "name": "EntryDetails", "statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}}]}