[{"uid": "1e7407e2f66e9c83", "name": "Test ApplicationContainer initialization", "time": {"start": 1750405275356, "stop": 1750405275386, "duration": 30}, "status": "broken", "severity": "critical"}, {"uid": "666a7450e6a06ab8", "name": "Test with_database_cleanup decorator for sync functions", "time": {"start": 1750405275572, "stop": 1750405275573, "duration": 1}, "status": "passed", "severity": "normal"}, {"uid": "26e67861079dc1ed", "name": "Test schema update functionality", "time": {"start": 1750405274559, "stop": 1750405274562, "duration": 3}, "status": "failed", "severity": "critical"}, {"uid": "7849036c2f78ddaa", "name": "Test schema override", "time": {"start": 1750405275186, "stop": 1750405275210, "duration": 24}, "status": "broken", "severity": "normal"}, {"uid": "798cbcdd93ddb460", "name": "Test auto-registration with lifecycle manager", "time": {"start": 1750405275054, "stop": 1750405275056, "duration": 2}, "status": "passed", "severity": "critical"}, {"uid": "f8d92e6405a94177", "name": "Test database_lifecycle context manager", "time": {"start": 1750405275528, "stop": 1750405275528, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "73a6856ba89ee634", "name": "Test PostgresSessionManager operations after close", "time": {"start": 1750405275608, "stop": 1750405275617, "duration": 9}, "status": "passed", "severity": "normal"}, {"uid": "525cb2afcb653ce3", "name": "Test build_entry_details function", "time": {"start": 1750405275519, "stop": 1750405275520, "duration": 1}, "status": "passed", "severity": "normal"}, {"uid": "decf358f50812dbe", "name": "Test with_database_cleanup decorator", "time": {"start": 1750405275554, "stop": 1750405275554, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b78044c6b3bef003", "name": "Test EntryDetails creation", "time": {"start": 1750405274524, "stop": 1750405274524, "duration": 0}, "status": "passed", "severity": "normal"}, {"uid": "d1c2d194120b79a2", "name": "Test database provider resolution", "time": {"start": 1750405275270, "stop": 1750405275303, "duration": 33}, "status": "broken", "severity": "critical"}, {"uid": "46c379376aea2dde", "name": "Test lifecycle manager provider", "time": {"start": 1750405275439, "stop": 1750405275463, "duration": 24}, "status": "broken", "severity": "normal"}, {"uid": "e95b4258278bc102", "name": "Test container initialization", "time": {"start": 1750405275082, "stop": 1750405275106, "duration": 24}, "status": "broken", "severity": "critical"}, {"uid": "be18eef980fb8205", "name": "Test EntryDetails string representation", "time": {"start": 1750405274538, "stop": 1750405274539, "duration": 1}, "status": "passed", "severity": "minor"}, {"uid": "1abd4ec7fad4ee5", "name": "Test lifecycle manager cleanup", "time": {"start": 1750405275063, "stop": 1750405275064, "duration": 1}, "status": "failed", "severity": "normal"}, {"uid": "966775e300a39d4e", "name": "Test PostgresSessionManager initialization", "time": {"start": 1750405274544, "stop": 1750405274551, "duration": 7}, "status": "passed", "severity": "critical"}, {"uid": "c19986838582c641", "name": "Test build_entry_details with missing entry", "time": {"start": 1750405275580, "stop": 1750405275584, "duration": 4}, "status": "failed", "severity": "critical"}, {"uid": "8b77d23f62e23ce1", "name": "Test EntryDetailsBuilder pattern", "time": {"start": 1750405274532, "stop": 1750405274532, "duration": 0}, "status": "passed", "severity": "normal"}]