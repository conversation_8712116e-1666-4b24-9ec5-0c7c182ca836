{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "children": [{"name": "Test schema update functionality", "uid": "26e67861079dc1ed", "parentUid": "b51da04eaba869f06c95aaebd50d60c4", "status": "failed", "time": {"start": 1750405274559, "stop": 1750405274562, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "b51da04eaba869f06c95aaebd50d60c4"}, {"name": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "children": [{"name": "Test lifecycle manager cleanup", "uid": "1abd4ec7fad4ee5", "parentUid": "ba89de3ff3cb707e604ce0566bd044b5", "status": "failed", "time": {"start": 1750405275063, "stop": 1750405275064, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "f8d92e6405a94177", "parentUid": "ba89de3ff3cb707e604ce0566bd044b5", "status": "failed", "time": {"start": 1750405275528, "stop": 1750405275528, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "decf358f50812dbe", "parentUid": "ba89de3ff3cb707e604ce0566bd044b5", "status": "failed", "time": {"start": 1750405275554, "stop": 1750405275554, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "ba89de3ff3cb707e604ce0566bd044b5"}, {"name": "Failed: DID NOT RAISE <class 'LookupError'>", "children": [{"name": "Test build_entry_details with missing entry", "uid": "c19986838582c641", "parentUid": "ec77e9d9804cf25a0c9400515deaccd6", "status": "failed", "time": {"start": 1750405275580, "stop": 1750405275584, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "ec77e9d9804cf25a0c9400515deaccd6"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}, {"name": "Test defects", "children": [{"name": "ModuleNotFoundError: No module named 'containers'", "children": [{"name": "Test container initialization", "uid": "e95b4258278bc102", "parentUid": "11ef970b8b3a59b749ceee0aece5e8c7", "status": "broken", "time": {"start": 1750405275082, "stop": 1750405275106, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "7849036c2f78ddaa", "parentUid": "11ef970b8b3a59b749ceee0aece5e8c7", "status": "broken", "time": {"start": 1750405275186, "stop": 1750405275210, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "d1c2d194120b79a2", "parentUid": "11ef970b8b3a59b749ceee0aece5e8c7", "status": "broken", "time": {"start": 1750405275270, "stop": 1750405275303, "duration": 33}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "11ef970b8b3a59b749ceee0aece5e8c7"}, {"name": "ModuleNotFoundError: No module named 'utility_code'", "children": [{"name": "Test ApplicationContainer initialization", "uid": "1e7407e2f66e9c83", "parentUid": "33312567973da69316fcab1fae26e4a5", "status": "broken", "time": {"start": 1750405275356, "stop": 1750405275386, "duration": 30}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "46c379376aea2dde", "parentUid": "33312567973da69316fcab1fae26e4a5", "status": "broken", "time": {"start": 1750405275439, "stop": 1750405275463, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "33312567973da69316fcab1fae26e4a5"}], "uid": "bdbf199525818fae7a8651db9eafe741"}]}