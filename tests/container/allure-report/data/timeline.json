{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "LAP-VISHALB", "children": [{"name": "28844-MainThread", "children": [{"name": "Test database provider resolution", "uid": "16db6ddc4f5944db", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "broken", "time": {"start": 1750405171792, "stop": 1750405171812, "duration": 20}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "161d9b8dc75b9e54", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "failed", "time": {"start": 1750405172026, "stop": 1750405172026, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test container initialization", "uid": "7f76e934a00d8294", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "broken", "time": {"start": 1750405171637, "stop": 1750405171660, "duration": 23}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "503239f1d0c4204c", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405171082, "stop": 1750405171082, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "a0da9e1e0d2fd204", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "broken", "time": {"start": 1750405171940, "stop": 1750405171961, "duration": 21}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "1d9bb3dba8d26e35", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405172105, "stop": 1750405172111, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "53b1f24d7520b48a", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405171074, "stop": 1750405171074, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "579832a6738c5233", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405172063, "stop": 1750405172065, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "53ebe4a84aaf1687", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "broken", "time": {"start": 1750405171715, "stop": 1750405171739, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "8754fd801858e0c4", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "failed", "time": {"start": 1750405172043, "stop": 1750405172044, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "ddf26cdee30213c2", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "failed", "time": {"start": 1750405171108, "stop": 1750405171112, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager initialization", "uid": "898dfd7395b3e0d4", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405171090, "stop": 1750405171100, "duration": 10}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details function", "uid": "3fb14793eef84a72", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405172017, "stop": 1750405172018, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "b2fd164ffbe3dabe", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "failed", "time": {"start": 1750405171615, "stop": 1750405171616, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test ApplicationContainer initialization", "uid": "9aaf9a96c8726711", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "broken", "time": {"start": 1750405171870, "stop": 1750405171893, "duration": 23}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails creation", "uid": "d7ccbabbd5403e0a", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405171067, "stop": 1750405171067, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test auto-registration with lifecycle manager", "uid": "75cc2d47e5cca934", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "passed", "time": {"start": 1750405171604, "stop": 1750405171607, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details with missing entry", "uid": "715aba7b36b54e09", "parentUid": "34c2f5a0f0fd63e5a9e5b11cf6beed02", "status": "failed", "time": {"start": 1750405172070, "stop": 1750405172074, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "34c2f5a0f0fd63e5a9e5b11cf6beed02"}, {"name": "25276-MainThread", "children": [{"name": "Test schema override", "uid": "2196fb1ac181893d", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "broken", "time": {"start": 1750404840317, "stop": 1750404840338, "duration": 21}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test ApplicationContainer initialization", "uid": "d7687c44b7a1752e", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "broken", "time": {"start": 1750404840473, "stop": 1750404840494, "duration": 21}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails creation", "uid": "710fd827c24b395f", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404839676, "stop": 1750404839677, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test container initialization", "uid": "1900ca2a70f86979", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "broken", "time": {"start": 1750404840233, "stop": 1750404840258, "duration": 25}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details function", "uid": "6f9d514f54aae8b6", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404840624, "stop": 1750404840625, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "41a152a2f997b73", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404840702, "stop": 1750404840710, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "bff369c4a7c14380", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "broken", "time": {"start": 1750404840551, "stop": 1750404840575, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test auto-registration with lifecycle manager", "uid": "664d8b6895654508", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404840211, "stop": 1750404840213, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details with missing entry", "uid": "841559dd3b9b53d0", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "failed", "time": {"start": 1750404840672, "stop": 1750404840675, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "ef64dd7247cc5730", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404840664, "stop": 1750404840666, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager initialization", "uid": "77b787032a569522", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404839694, "stop": 1750404839700, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "b7abfc98b8a413ac", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404839682, "stop": 1750404839683, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "d0672978665526c3", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "failed", "time": {"start": 1750404840650, "stop": 1750404840651, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "37365f49b94de4ab", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "broken", "time": {"start": 1750404840400, "stop": 1750404840421, "duration": 21}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "5ef8db6a25625011", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "failed", "time": {"start": 1750404840218, "stop": 1750404840219, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "e4e3177a591443df", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "failed", "time": {"start": 1750404840631, "stop": 1750404840632, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "61a831ac2d822b15", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "passed", "time": {"start": 1750404839688, "stop": 1750404839689, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "1be13b141652509e", "parentUid": "3856dbd40eba2ef8a014559eda1b2a2f", "status": "failed", "time": {"start": 1750404839707, "stop": 1750404839709, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "3856dbd40eba2ef8a014559eda1b2a2f"}, {"name": "29004-MainThread", "children": [{"name": "Test auto-registration with lifecycle manager", "uid": "972ea94ea77d1bbe", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404502698, "stop": 1750404502702, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "ca7d0d37bac7e44a", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "failed", "time": {"start": 1750404502148, "stop": 1750404502151, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "37ed98276f53285e", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "broken", "time": {"start": 1750404502852, "stop": 1750404502872, "duration": 20}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "6864012529e9406f", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404502125, "stop": 1750404502126, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details with missing entry", "uid": "7ddff93bd2a6be91", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "failed", "time": {"start": 1750404503235, "stop": 1750404503238, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "d1ab940467585bb", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "failed", "time": {"start": 1750404502707, "stop": 1750404502708, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details function", "uid": "f1594564d5fc4f46", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404503181, "stop": 1750404503182, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager initialization", "uid": "df1b1f3aed62de27", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404502137, "stop": 1750404502142, "duration": 5}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails creation", "uid": "fef9334c75522178", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404502117, "stop": 1750404502117, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "9fef5dd5f331bf3c", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404503227, "stop": 1750404503227, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test ApplicationContainer initialization", "uid": "e71858f9bbaf9f30", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "broken", "time": {"start": 1750404503023, "stop": 1750404503049, "duration": 26}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "d1028f5d20342576", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "failed", "time": {"start": 1750404503206, "stop": 1750404503206, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "e68020b239b0da85", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404502132, "stop": 1750404502132, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "8838ae9746f325a0", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "failed", "time": {"start": 1750404503188, "stop": 1750404503189, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "1911bcae2d5ccfd4", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "broken", "time": {"start": 1750404503104, "stop": 1750404503125, "duration": 21}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "537dc27415d8ab4e", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "passed", "time": {"start": 1750404503264, "stop": 1750404503271, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test container initialization", "uid": "5d138174970eb642", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "broken", "time": {"start": 1750404502727, "stop": 1750404502758, "duration": 31}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "d1f6f04f1a0dd87", "parentUid": "87534de0a6ce571f60a24972687bb921", "status": "broken", "time": {"start": 1750404502930, "stop": 1750404502955, "duration": 25}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "87534de0a6ce571f60a24972687bb921"}, {"name": "1920-MainThread", "children": [{"name": "Test PostgresSessionManager initialization", "uid": "8e527df3a2cd2db3", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404756625, "stop": 1750404756632, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "1f988c5635bd338a", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404756619, "stop": 1750404756619, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "32495af9176adacf", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404757686, "stop": 1750404757720, "duration": 34}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "dcf584352238849b", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "broken", "time": {"start": 1750404757356, "stop": 1750404757379, "duration": 23}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "189d7ee064ff8bf7", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "broken", "time": {"start": 1750404757277, "stop": 1750404757300, "duration": 23}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "1e436975fb617391", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "failed", "time": {"start": 1750404757608, "stop": 1750404757608, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "a03e9862bb36e5a3", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404756614, "stop": 1750404756615, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details with missing entry", "uid": "ad276dd007f3b40e", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "failed", "time": {"start": 1750404757650, "stop": 1750404757653, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details function", "uid": "e2713dd49eb58f", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404757599, "stop": 1750404757599, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test auto-registration with lifecycle manager", "uid": "9452c3a2d9f7b6ad", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404757154, "stop": 1750404757157, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails creation", "uid": "f73c9b60f438d145", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404756607, "stop": 1750404756607, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test container initialization", "uid": "7797747a8a5c8b41", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "broken", "time": {"start": 1750404757189, "stop": 1750404757216, "duration": 27}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "a21a046340610248", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "failed", "time": {"start": 1750404756641, "stop": 1750404756647, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test ApplicationContainer initialization", "uid": "be6a73ad914792f5", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "broken", "time": {"start": 1750404757430, "stop": 1750404757457, "duration": 27}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "99723b387b7162eb", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "failed", "time": {"start": 1750404757624, "stop": 1750404757625, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "eb542df05dbb7b03", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "broken", "time": {"start": 1750404757513, "stop": 1750404757539, "duration": 26}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "e9540e792f031536", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "passed", "time": {"start": 1750404757643, "stop": 1750404757644, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "205152b5e1cdbe92", "parentUid": "17e0864deaff16bf08091b6d67a27060", "status": "failed", "time": {"start": 1750404757166, "stop": 1750404757168, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "17e0864deaff16bf08091b6d67a27060"}, {"name": "26244-MainThread", "children": [{"name": "Test ApplicationContainer initialization", "uid": "1e7407e2f66e9c83", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "broken", "time": {"start": 1750405275356, "stop": 1750405275386, "duration": 30}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "1abd4ec7fad4ee5", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "failed", "time": {"start": 1750405275063, "stop": 1750405275064, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details with missing entry", "uid": "c19986838582c641", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "failed", "time": {"start": 1750405275580, "stop": 1750405275584, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "f8d92e6405a94177", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "failed", "time": {"start": 1750405275528, "stop": 1750405275528, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "26e67861079dc1ed", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "failed", "time": {"start": 1750405274559, "stop": 1750405274562, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager initialization", "uid": "966775e300a39d4e", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405274544, "stop": 1750405274551, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test container initialization", "uid": "e95b4258278bc102", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "broken", "time": {"start": 1750405275082, "stop": 1750405275106, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "7849036c2f78ddaa", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "broken", "time": {"start": 1750405275186, "stop": 1750405275210, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test auto-registration with lifecycle manager", "uid": "798cbcdd93ddb460", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405275054, "stop": 1750405275056, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test build_entry_details function", "uid": "525cb2afcb653ce3", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405275519, "stop": 1750405275520, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "666a7450e6a06ab8", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405275572, "stop": 1750405275573, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "73a6856ba89ee634", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405275608, "stop": 1750405275617, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "decf358f50812dbe", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "failed", "time": {"start": 1750405275554, "stop": 1750405275554, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails creation", "uid": "b78044c6b3bef003", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405274524, "stop": 1750405274524, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "46c379376aea2dde", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "broken", "time": {"start": 1750405275439, "stop": 1750405275463, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "8b77d23f62e23ce1", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405274532, "stop": 1750405274532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "d1c2d194120b79a2", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "broken", "time": {"start": 1750405275270, "stop": 1750405275303, "duration": 33}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "be18eef980fb8205", "parentUid": "c64480046d6c776e39f5a8c8e3815e35", "status": "passed", "time": {"start": 1750405274538, "stop": 1750405274539, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "c64480046d6c776e39f5a8c8e3815e35"}], "uid": "0cdf8c0b4624ae86996dd243407ae34b"}]}