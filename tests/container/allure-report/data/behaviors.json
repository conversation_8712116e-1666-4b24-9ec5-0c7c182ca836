{"uid": "b1a8273437954620fa374b796ffaacdd", "name": "behaviors", "children": [{"name": "EntryDetails", "children": [{"name": "Entry Details Management", "children": [{"name": "Test EntryDetails creation", "uid": "b78044c6b3bef003", "parentUid": "874b811be22abf74dfe5d6e2ab893f80", "status": "passed", "time": {"start": 1750405274524, "stop": 1750405274524, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "8b77d23f62e23ce1", "parentUid": "874b811be22abf74dfe5d6e2ab893f80", "status": "passed", "time": {"start": 1750405274532, "stop": 1750405274532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "be18eef980fb8205", "parentUid": "874b811be22abf74dfe5d6e2ab893f80", "status": "passed", "time": {"start": 1750405274538, "stop": 1750405274539, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "874b811be22abf74dfe5d6e2ab893f80"}], "uid": "03838136887a03fb82e180b4c308c821"}, {"name": "PostgresSessionManager", "children": [{"name": "Database Session Management", "children": [{"name": "Test PostgresSessionManager initialization", "uid": "966775e300a39d4e", "parentUid": "213a8e75d4237d2004606c35ca1af649", "status": "passed", "time": {"start": 1750405274544, "stop": 1750405274551, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "26e67861079dc1ed", "parentUid": "213a8e75d4237d2004606c35ca1af649", "status": "failed", "time": {"start": 1750405274559, "stop": 1750405274562, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "213a8e75d4237d2004606c35ca1af649"}], "uid": "1d14c8ecf7b0fbb6662d939b8b8953c3"}, {"name": "ManagedPostgresSessionManager", "children": [{"name": "Managed Database Sessions", "children": [{"name": "Test auto-registration with lifecycle manager", "uid": "798cbcdd93ddb460", "parentUid": "01b8a4295a4c111f3d57fc21f0a6efff", "status": "passed", "time": {"start": 1750405275054, "stop": 1750405275056, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "1abd4ec7fad4ee5", "parentUid": "01b8a4295a4c111f3d57fc21f0a6efff", "status": "failed", "time": {"start": 1750405275063, "stop": 1750405275064, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "01b8a4295a4c111f3d57fc21f0a6efff"}], "uid": "aedfae7c917c649d5e84ab17847c741b"}, {"name": "DatabaseSessionManagerContainer", "children": [{"name": "Container Dependency Injection", "children": [{"name": "Test container initialization", "uid": "e95b4258278bc102", "parentUid": "a6d5b68affeab5d42dfbc33d87033add", "status": "broken", "time": {"start": 1750405275082, "stop": 1750405275106, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "7849036c2f78ddaa", "parentUid": "a6d5b68affeab5d42dfbc33d87033add", "status": "broken", "time": {"start": 1750405275186, "stop": 1750405275210, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "d1c2d194120b79a2", "parentUid": "a6d5b68affeab5d42dfbc33d87033add", "status": "broken", "time": {"start": 1750405275270, "stop": 1750405275303, "duration": 33}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "a6d5b68affeab5d42dfbc33d87033add"}], "uid": "c367718f5899cf27cb25c1b286144a4a"}, {"name": "ApplicationContainer", "children": [{"name": "Application-wide Dependency Injection", "children": [{"name": "Test ApplicationContainer initialization", "uid": "1e7407e2f66e9c83", "parentUid": "e34cbb23b5719bd9a4db3ed32379bb33", "status": "broken", "time": {"start": 1750405275356, "stop": 1750405275386, "duration": 30}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "46c379376aea2dde", "parentUid": "e34cbb23b5719bd9a4db3ed32379bb33", "status": "broken", "time": {"start": 1750405275439, "stop": 1750405275463, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "e34cbb23b5719bd9a4db3ed32379bb33"}], "uid": "0fa9b6682277ff6d85bf97b7c04a63cc"}, {"name": "Utility Functions", "children": [{"name": "Helper Functions and Decorators", "children": [{"name": "Test build_entry_details function", "uid": "525cb2afcb653ce3", "parentUid": "a3de8e5aff047c0f2cec5f3a9a456d5f", "status": "passed", "time": {"start": 1750405275519, "stop": 1750405275520, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "f8d92e6405a94177", "parentUid": "a3de8e5aff047c0f2cec5f3a9a456d5f", "status": "failed", "time": {"start": 1750405275528, "stop": 1750405275528, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "decf358f50812dbe", "parentUid": "a3de8e5aff047c0f2cec5f3a9a456d5f", "status": "failed", "time": {"start": 1750405275554, "stop": 1750405275554, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "666a7450e6a06ab8", "parentUid": "a3de8e5aff047c0f2cec5f3a9a456d5f", "status": "passed", "time": {"start": 1750405275572, "stop": 1750405275573, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "a3de8e5aff047c0f2cec5f3a9a456d5f"}], "uid": "0a5e45d8fd7a5cc126497acbd4503f9c"}, {"name": "Erro<PERSON>", "children": [{"name": "Exception Handling and Edge Cases", "children": [{"name": "Test build_entry_details with missing entry", "uid": "c19986838582c641", "parentUid": "d6eb9733acb046559886498e4a463695", "status": "failed", "time": {"start": 1750405275580, "stop": 1750405275584, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "73a6856ba89ee634", "parentUid": "d6eb9733acb046559886498e4a463695", "status": "passed", "time": {"start": 1750405275608, "stop": 1750405275617, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "d6eb9733acb046559886498e4a463695"}], "uid": "f5cbfe097c38ce6d36a240ecb72ab65b"}]}