{"uid": "710fd827c24b395f", "name": "Test EntryDetails creation", "fullName": "test_containers_comprehensive.TestEntryDetails#test_entry_details_creation", "historyId": "711261e787fa8e9b43d1e5a6f6fbc321", "time": {"start": 1750404839676, "stop": 1750404839677, "duration": 1}, "description": "Test creating EntryDetails with all fields.", "descriptionHtml": "<p>Test creating EntryDetails with all fields.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404839563, "stop": 1750404839673, "duration": 110}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404839674, "stop": 1750404839674, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "25276-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "710fd827c24b395f.json", "parameterValues": []}