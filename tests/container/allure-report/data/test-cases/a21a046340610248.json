{"uid": "a21a046340610248", "name": "Test schema update functionality", "fullName": "test_containers_comprehensive.TestPostgresSessionManager#test_update_schema", "historyId": "667f855d73df66f92a573378a154c0be", "time": {"start": 1750404756641, "stop": 1750404756647, "duration": 6}, "description": "Test schema update functionality.", "descriptionHtml": "<p>Test schema update functionality.</p>\n", "status": "failed", "statusMessage": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "statusTrace": "C:\\Program Files\\Python312\\Lib\\unittest\\mock.py:940: in assert_called_with\n    raise AssertionError(error_message)\nE   AssertionError: expected call not found.\nE   Expected: execution_options(schema_translate_map={None: 'new_schema'})\nE     Actual: not called.\n\nDuring handling of the above exception, another exception occurred:\ntest_containers_comprehensive.py:218: in test_update_schema\n    mock_sync_engine.execution_options.assert_called_with(\nE   AssertionError: expected call not found.\nE   Expected: execution_options(schema_translate_map={None: 'new_schema'})\nE     Actual: not called.", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404756491, "stop": 1750404756603, "duration": 112}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404756604, "stop": 1750404756604, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750404756639, "stop": 1750404756640, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Database Session Management"}, {"name": "feature", "value": "PostgresSessionManager"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "1920-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "a21a046340610248.json", "parameterValues": []}