{"uid": "1e7407e2f66e9c83", "name": "Test ApplicationContainer initialization", "fullName": "test_containers_comprehensive.TestApplicationContainer#test_application_container_init", "historyId": "3bdd64993aea75f2c5eef9f52a148dac", "time": {"start": 1750405275356, "stop": 1750405275386, "duration": 30}, "description": "Test ApplicationContainer initialization.", "descriptionHtml": "<p>Test ApplicationContainer initialization.</p>\n", "status": "broken", "statusMessage": "ModuleNotFoundError: No module named 'utility_code'", "statusTrace": "test_containers_comprehensive.py:343: in test_application_container_init\n    container = ApplicationContainer()\n                ^^^^^^^^^^^^^^^^^^^^^^\nsrc/dependency_injector/containers.pyx:735: in dependency_injector.containers.DeclarativeContainer.__new__\n    ???\nsrc/dependency_injector/containers.pyx:287: in dependency_injector.containers.DynamicContainer.wire\n    ???\nsrc/dependency_injector/containers.pyx:903: in dependency_injector.containers._resolve_string_imports\n    ???\nC:\\Program Files\\Python312\\Lib\\importlib\\__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1324: in _find_and_load_unlocked\n    ???\nE   ModuleNotFoundError: No module named 'utility_code'", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "9aaf9a96c8726711", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750405171870, "stop": 1750405171893, "duration": 23}}, {"uid": "d7687c44b7a1752e", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750404840473, "stop": 1750404840494, "duration": 21}}, {"uid": "be6a73ad914792f5", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750404757430, "stop": 1750404757457, "duration": 27}}, {"uid": "e71858f9bbaf9f30", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750404503023, "stop": 1750404503049, "duration": 26}}], "categories": [{"name": "Test defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "1e7407e2f66e9c83.json", "parameterValues": []}