{"uid": "7ddff93bd2a6be91", "name": "Test build_entry_details with missing entry", "fullName": "test_containers_comprehensive.TestErrorHandling#test_build_entry_details_missing_entry", "historyId": "9731d23a9ccf550d9742979f13bb77c0", "time": {"start": 1750404503235, "stop": 1750404503238, "duration": 3}, "description": "Test build_entry_details when entry is not found.", "descriptionHtml": "<p>Test build_entry_details when entry is not found.</p>\n", "status": "failed", "statusMessage": "Failed: DID NOT RAISE <class 'LookupError'>", "statusTrace": "test_containers_comprehensive.py:452: in test_build_entry_details_missing_entry\n    with pytest.raises(LookupError, match=\"No entry found with given title\"):\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   Failed: DID NOT RAISE <class 'LookupError'>", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404502005, "stop": 1750404502111, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404502112, "stop": 1750404502112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "<PERSON><PERSON> Manager", "time": {"start": 1750404503234, "stop": 1750404503234, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "29004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "7ddff93bd2a6be91.json", "parameterValues": []}