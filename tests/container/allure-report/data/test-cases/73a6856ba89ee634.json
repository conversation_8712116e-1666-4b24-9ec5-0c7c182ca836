{"uid": "73a6856ba89ee634", "name": "Test PostgresSessionManager operations after close", "fullName": "test_containers_comprehensive.TestErrorHandling#test_operations_after_close", "historyId": "a704265117f6b098d739b7bb66a45c07", "time": {"start": 1750405275608, "stop": 1750405275617, "duration": 9}, "description": "Test that operations fail after manager is closed.", "descriptionHtml": "<p>Test that operations fail after manager is closed.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405275607, "stop": 1750405275607, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "1d9bb3dba8d26e35", "status": "passed", "time": {"start": 1750405172105, "stop": 1750405172111, "duration": 6}}, {"uid": "41a152a2f997b73", "status": "passed", "time": {"start": 1750404840702, "stop": 1750404840710, "duration": 8}}, {"uid": "32495af9176adacf", "status": "passed", "time": {"start": 1750404757686, "stop": 1750404757720, "duration": 34}}, {"uid": "537dc27415d8ab4e", "status": "passed", "time": {"start": 1750404503264, "stop": 1750404503271, "duration": 7}}], "categories": [], "tags": []}, "source": "73a6856ba89ee634.json", "parameterValues": []}