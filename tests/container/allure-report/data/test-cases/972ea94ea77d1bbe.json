{"uid": "972ea94ea77d1bbe", "name": "Test auto-registration with lifecycle manager", "fullName": "test_containers_comprehensive.TestManagedPostgresSessionManager#test_auto_registration", "historyId": "c6a87faeb7e4aa411c80e7f1e9fc5384", "time": {"start": 1750404502698, "stop": 1750404502702, "duration": 4}, "description": "Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.", "descriptionHtml": "<p>Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404502005, "stop": 1750404502111, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404502112, "stop": 1750404502112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750404502697, "stop": 1750404502697, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "story", "value": "Managed Database Sessions"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "29004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "972ea94ea77d1bbe.json", "parameterValues": []}