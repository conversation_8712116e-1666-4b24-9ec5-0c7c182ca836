{"uid": "8b77d23f62e23ce1", "name": "Test EntryDetailsBuilder pattern", "fullName": "test_containers_comprehensive.TestEntryDetails#test_entry_details_builder", "historyId": "7e82c8fd69e79efea2424a926235c857", "time": {"start": 1750405274532, "stop": 1750405274532, "duration": 0}, "description": "Test the builder pattern for EntryDetails.", "descriptionHtml": "<p>Test the builder pattern for EntryDetails.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "53b1f24d7520b48a", "status": "passed", "time": {"start": 1750405171074, "stop": 1750405171074, "duration": 0}}, {"uid": "b7abfc98b8a413ac", "status": "passed", "time": {"start": 1750404839682, "stop": 1750404839683, "duration": 1}}, {"uid": "a03e9862bb36e5a3", "status": "passed", "time": {"start": 1750404756614, "stop": 1750404756615, "duration": 1}}, {"uid": "6864012529e9406f", "status": "passed", "time": {"start": 1750404502125, "stop": 1750404502126, "duration": 1}}], "categories": [], "tags": []}, "source": "8b77d23f62e23ce1.json", "parameterValues": []}