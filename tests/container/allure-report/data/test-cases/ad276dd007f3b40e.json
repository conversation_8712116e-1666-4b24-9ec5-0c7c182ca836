{"uid": "ad276dd007f3b40e", "name": "Test build_entry_details with missing entry", "fullName": "test_containers_comprehensive.TestErrorHandling#test_build_entry_details_missing_entry", "historyId": "9731d23a9ccf550d9742979f13bb77c0", "time": {"start": 1750404757650, "stop": 1750404757653, "duration": 3}, "description": "Test build_entry_details when entry is not found.", "descriptionHtml": "<p>Test build_entry_details when entry is not found.</p>\n", "status": "failed", "statusMessage": "Failed: DID NOT RAISE <class 'LookupError'>", "statusTrace": "test_containers_comprehensive.py:452: in test_build_entry_details_missing_entry\n    with pytest.raises(LookupError, match=\"No entry found with given title\"):\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   Failed: DID NOT RAISE <class 'LookupError'>", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404756491, "stop": 1750404756603, "duration": 112}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404756604, "stop": 1750404756604, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "<PERSON><PERSON> Manager", "time": {"start": 1750404757649, "stop": 1750404757649, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "1920-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "ad276dd007f3b40e.json", "parameterValues": []}