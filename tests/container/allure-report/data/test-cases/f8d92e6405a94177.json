{"uid": "f8d92e6405a94177", "name": "Test database_lifecycle context manager", "fullName": "test_containers_comprehensive.TestUtilityFunctions#test_database_lifecycle_context_manager", "historyId": "ace459ad0a7d9a1e3dfa090e2a9dfc94", "time": {"start": 1750405275528, "stop": 1750405275528, "duration": 0}, "description": "Test database_lifecycle async context manager.", "descriptionHtml": "<p>Test database_lifecycle async context manager.</p>\n", "status": "failed", "statusMessage": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "statusTrace": "async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "161d9b8dc75b9e54", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750405172026, "stop": 1750405172026, "duration": 0}}, {"uid": "e4e3177a591443df", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750404840631, "stop": 1750404840632, "duration": 1}}, {"uid": "1e436975fb617391", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750404757608, "stop": 1750404757608, "duration": 0}}, {"uid": "8838ae9746f325a0", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750404503188, "stop": 1750404503189, "duration": 1}}], "categories": [{"name": "Product defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "f8d92e6405a94177.json", "parameterValues": []}