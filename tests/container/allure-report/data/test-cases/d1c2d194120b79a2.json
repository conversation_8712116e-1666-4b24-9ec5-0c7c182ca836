{"uid": "d1c2d194120b79a2", "name": "Test database provider resolution", "fullName": "test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_database_provider_resolution", "historyId": "a0f2250ed0bdebb68797307cbf30190f", "time": {"start": 1750405275270, "stop": 1750405275303, "duration": 33}, "description": "Test that database providers resolve correctly.", "descriptionHtml": "<p>Test that database providers resolve correctly.</p>\n", "status": "broken", "statusMessage": "ModuleNotFoundError: No module named 'containers'", "statusTrace": "test_containers_comprehensive.py:316: in test_database_provider_resolution\n    container = DatabaseSessionManagerContainer()\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsrc/dependency_injector/containers.pyx:735: in dependency_injector.containers.DeclarativeContainer.__new__\n    ???\nsrc/dependency_injector/containers.pyx:287: in dependency_injector.containers.DynamicContainer.wire\n    ???\nsrc/dependency_injector/containers.pyx:903: in dependency_injector.containers._resolve_string_imports\n    ???\nC:\\Program Files\\Python312\\Lib\\importlib\\__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1324: in _find_and_load_unlocked\n    ???\nE   ModuleNotFoundError: No module named 'containers'", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405275269, "stop": 1750405275269, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "16db6ddc4f5944db", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750405171792, "stop": 1750405171812, "duration": 20}}, {"uid": "37365f49b94de4ab", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750404840400, "stop": 1750404840421, "duration": 21}}, {"uid": "dcf584352238849b", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750404757356, "stop": 1750404757379, "duration": 23}}, {"uid": "d1f6f04f1a0dd87", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750404502930, "stop": 1750404502955, "duration": 25}}], "categories": [{"name": "Test defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "d1c2d194120b79a2.json", "parameterValues": []}