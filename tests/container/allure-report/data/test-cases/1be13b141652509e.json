{"uid": "1be13b141652509e", "name": "Test schema update functionality", "fullName": "test_containers_comprehensive.TestPostgresSessionManager#test_update_schema", "historyId": "667f855d73df66f92a573378a154c0be", "time": {"start": 1750404839707, "stop": 1750404839709, "duration": 2}, "description": "Test schema update functionality.", "descriptionHtml": "<p>Test schema update functionality.</p>\n", "status": "failed", "statusMessage": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "statusTrace": "C:\\Program Files\\Python312\\Lib\\unittest\\mock.py:940: in assert_called_with\n    raise AssertionError(error_message)\nE   AssertionError: expected call not found.\nE   Expected: execution_options(schema_translate_map={None: 'new_schema'})\nE     Actual: not called.\n\nDuring handling of the above exception, another exception occurred:\ntest_containers_comprehensive.py:218: in test_update_schema\n    mock_sync_engine.execution_options.assert_called_with(\nE   AssertionError: expected call not found.\nE   Expected: execution_options(schema_translate_map={None: 'new_schema'})\nE     Actual: not called.", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404839563, "stop": 1750404839673, "duration": 110}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404839674, "stop": 1750404839674, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750404839705, "stop": 1750404839705, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Database Session Management"}, {"name": "feature", "value": "PostgresSessionManager"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "25276-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "1be13b141652509e.json", "parameterValues": []}