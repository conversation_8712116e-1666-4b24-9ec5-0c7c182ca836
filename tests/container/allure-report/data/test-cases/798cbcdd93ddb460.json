{"uid": "798cbcdd93ddb460", "name": "Test auto-registration with lifecycle manager", "fullName": "test_containers_comprehensive.TestManagedPostgresSessionManager#test_auto_registration", "historyId": "c6a87faeb7e4aa411c80e7f1e9fc5384", "time": {"start": 1750405275054, "stop": 1750405275056, "duration": 2}, "description": "Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.", "descriptionHtml": "<p>Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405275053, "stop": 1750405275054, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Managed Database Sessions"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "75cc2d47e5cca934", "status": "passed", "time": {"start": 1750405171604, "stop": 1750405171607, "duration": 3}}, {"uid": "664d8b6895654508", "status": "passed", "time": {"start": 1750404840211, "stop": 1750404840213, "duration": 2}}, {"uid": "9452c3a2d9f7b6ad", "status": "passed", "time": {"start": 1750404757154, "stop": 1750404757157, "duration": 3}}, {"uid": "972ea94ea77d1bbe", "status": "passed", "time": {"start": 1750404502698, "stop": 1750404502702, "duration": 4}}], "categories": [], "tags": []}, "source": "798cbcdd93ddb460.json", "parameterValues": []}