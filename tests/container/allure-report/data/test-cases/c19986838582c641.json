{"uid": "c19986838582c641", "name": "Test build_entry_details with missing entry", "fullName": "test_containers_comprehensive.TestErrorHandling#test_build_entry_details_missing_entry", "historyId": "9731d23a9ccf550d9742979f13bb77c0", "time": {"start": 1750405275580, "stop": 1750405275584, "duration": 4}, "description": "Test build_entry_details when entry is not found.", "descriptionHtml": "<p>Test build_entry_details when entry is not found.</p>\n", "status": "failed", "statusMessage": "Failed: DID NOT RAISE <class 'LookupError'>", "statusTrace": "test_containers_comprehensive.py:452: in test_build_entry_details_missing_entry\n    with pytest.raises(LookupError, match=\"No entry found with given title\"):\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   Failed: DID NOT RAISE <class 'LookupError'>", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "<PERSON><PERSON> Manager", "time": {"start": 1750405275579, "stop": 1750405275579, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "715aba7b36b54e09", "status": "failed", "statusDetails": "Failed: DID NOT RAISE <class 'LookupError'>", "time": {"start": 1750405172070, "stop": 1750405172074, "duration": 4}}, {"uid": "841559dd3b9b53d0", "status": "failed", "statusDetails": "Failed: DID NOT RAISE <class 'LookupError'>", "time": {"start": 1750404840672, "stop": 1750404840675, "duration": 3}}, {"uid": "ad276dd007f3b40e", "status": "failed", "statusDetails": "Failed: DID NOT RAISE <class 'LookupError'>", "time": {"start": 1750404757650, "stop": 1750404757653, "duration": 3}}, {"uid": "7ddff93bd2a6be91", "status": "failed", "statusDetails": "Failed: DID NOT RAISE <class 'LookupError'>", "time": {"start": 1750404503235, "stop": 1750404503238, "duration": 3}}], "categories": [{"name": "Product defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "c19986838582c641.json", "parameterValues": []}