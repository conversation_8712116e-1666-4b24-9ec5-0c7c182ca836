{"uid": "26e67861079dc1ed", "name": "Test schema update functionality", "fullName": "test_containers_comprehensive.TestPostgresSessionManager#test_update_schema", "historyId": "667f855d73df66f92a573378a154c0be", "time": {"start": 1750405274559, "stop": 1750405274562, "duration": 3}, "description": "Test schema update functionality.", "descriptionHtml": "<p>Test schema update functionality.</p>\n", "status": "failed", "statusMessage": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "statusTrace": "C:\\Program Files\\Python312\\Lib\\unittest\\mock.py:940: in assert_called_with\n    raise AssertionError(error_message)\nE   AssertionError: expected call not found.\nE   Expected: execution_options(schema_translate_map={None: 'new_schema'})\nE     Actual: not called.\n\nDuring handling of the above exception, another exception occurred:\ntest_containers_comprehensive.py:218: in test_update_schema\n    mock_sync_engine.execution_options.assert_called_with(\nE   AssertionError: expected call not found.\nE   Expected: execution_options(schema_translate_map={None: 'new_schema'})\nE     Actual: not called.", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405274559, "stop": 1750405274559, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "PostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Database Session Management"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ddf26cdee30213c2", "status": "failed", "statusDetails": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "time": {"start": 1750405171108, "stop": 1750405171112, "duration": 4}}, {"uid": "1be13b141652509e", "status": "failed", "statusDetails": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "time": {"start": 1750404839707, "stop": 1750404839709, "duration": 2}}, {"uid": "a21a046340610248", "status": "failed", "statusDetails": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "time": {"start": 1750404756641, "stop": 1750404756647, "duration": 6}}, {"uid": "ca7d0d37bac7e44a", "status": "failed", "statusDetails": "AssertionError: expected call not found.\nExpected: execution_options(schema_translate_map={None: 'new_schema'})\n  Actual: not called.", "time": {"start": 1750404502148, "stop": 1750404502151, "duration": 3}}], "categories": [{"name": "Product defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "26e67861079dc1ed.json", "parameterValues": []}