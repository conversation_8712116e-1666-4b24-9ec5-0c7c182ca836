{"uid": "8838ae9746f325a0", "name": "Test database_lifecycle context manager", "fullName": "test_containers_comprehensive.TestUtilityFunctions#test_database_lifecycle_context_manager", "historyId": "ace459ad0a7d9a1e3dfa090e2a9dfc94", "time": {"start": 1750404503188, "stop": 1750404503189, "duration": 1}, "description": "Test database_lifecycle async context manager.", "descriptionHtml": "<p>Test database_lifecycle async context manager.</p>\n", "status": "failed", "statusMessage": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "statusTrace": "async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404502005, "stop": 1750404502111, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404502112, "stop": 1750404502112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "29004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "8838ae9746f325a0.json", "parameterValues": []}