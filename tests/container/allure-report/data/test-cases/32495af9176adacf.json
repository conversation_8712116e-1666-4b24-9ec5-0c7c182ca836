{"uid": "32495af9176adacf", "name": "Test PostgresSessionManager operations after close", "fullName": "test_containers_comprehensive.TestErrorHandling#test_operations_after_close", "historyId": "a704265117f6b098d739b7bb66a45c07", "time": {"start": 1750404757686, "stop": 1750404757720, "duration": 34}, "description": "Test that operations fail after manager is closed.", "descriptionHtml": "<p>Test that operations fail after manager is closed.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404756491, "stop": 1750404756603, "duration": 112}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404756604, "stop": 1750404756604, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750404757685, "stop": 1750404757685, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "1920-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "32495af9176adacf.json", "parameterValues": []}