{"uid": "e9540e792f031536", "name": "Test with_database_cleanup decorator for sync functions", "fullName": "test_containers_comprehensive.TestUtilityFunctions#test_with_database_cleanup_decorator_sync", "historyId": "4e34b51e9dba8f279f78022b79d49834", "time": {"start": 1750404757643, "stop": 1750404757644, "duration": 1}, "description": "Test with_database_cleanup decorator for synchronous functions.", "descriptionHtml": "<p>Test with_database_cleanup decorator for synchronous functions.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404756491, "stop": 1750404756603, "duration": 112}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404756604, "stop": 1750404756604, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Helper Functions and Decorators"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Utility Functions"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "1920-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "e9540e792f031536.json", "parameterValues": []}