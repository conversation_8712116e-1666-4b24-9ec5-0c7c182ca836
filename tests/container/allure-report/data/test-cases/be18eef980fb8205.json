{"uid": "be18eef980fb8205", "name": "Test EntryDetails string representation", "fullName": "test_containers_comprehensive.TestEntryDetails#test_entry_details_repr", "historyId": "5aebb147873c0d7c9c59acb5342ef611", "time": {"start": 1750405274538, "stop": 1750405274539, "duration": 1}, "description": "Test string representation hides password.", "descriptionHtml": "<p>Test string representation hides password.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "minor"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "minor", "retries": [{"uid": "503239f1d0c4204c", "status": "passed", "time": {"start": 1750405171082, "stop": 1750405171082, "duration": 0}}, {"uid": "61a831ac2d822b15", "status": "passed", "time": {"start": 1750404839688, "stop": 1750404839689, "duration": 1}}, {"uid": "1f988c5635bd338a", "status": "passed", "time": {"start": 1750404756619, "stop": 1750404756619, "duration": 0}}, {"uid": "e68020b239b0da85", "status": "passed", "time": {"start": 1750404502132, "stop": 1750404502132, "duration": 0}}], "categories": [], "tags": []}, "source": "be18eef980fb8205.json", "parameterValues": []}