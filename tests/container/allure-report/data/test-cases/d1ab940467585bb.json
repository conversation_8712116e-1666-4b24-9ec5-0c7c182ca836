{"uid": "d1ab940467585bb", "name": "Test lifecycle manager cleanup", "fullName": "test_containers_comprehensive.TestManagedPostgresSessionManager#test_lifecycle_manager_cleanup", "historyId": "9181c6e5277604e01e981a4ec70d70b1", "time": {"start": 1750404502707, "stop": 1750404502708, "duration": 1}, "description": "Test lifecycle manager cleanup functionality.", "descriptionHtml": "<p>Test lifecycle manager cleanup functionality.</p>\n", "status": "failed", "statusMessage": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "statusTrace": "async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404502005, "stop": 1750404502111, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404502112, "stop": 1750404502112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750404502707, "stop": 1750404502707, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "story", "value": "Managed Database Sessions"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "29004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "d1ab940467585bb.json", "parameterValues": []}