{"uid": "e95b4258278bc102", "name": "Test container initialization", "fullName": "test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_container_initialization", "historyId": "adabfe08d005a1c3378cfbfd327eac3c", "time": {"start": 1750405275082, "stop": 1750405275106, "duration": 24}, "description": "Test DatabaseSessionManagerContainer initialization.", "descriptionHtml": "<p>Test DatabaseSessionManagerContainer initialization.</p>\n", "status": "broken", "statusMessage": "ModuleNotFoundError: No module named 'containers'", "statusTrace": "test_containers_comprehensive.py:288: in test_container_initialization\n    container = DatabaseSessionManagerContainer()\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsrc/dependency_injector/containers.pyx:735: in dependency_injector.containers.DeclarativeContainer.__new__\n    ???\nsrc/dependency_injector/containers.pyx:287: in dependency_injector.containers.DynamicContainer.wire\n    ???\nsrc/dependency_injector/containers.pyx:903: in dependency_injector.containers._resolve_string_imports\n    ???\nC:\\Program Files\\Python312\\Lib\\importlib\\__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1324: in _find_and_load_unlocked\n    ???\nE   ModuleNotFoundError: No module named 'containers'", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "7f76e934a00d8294", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750405171637, "stop": 1750405171660, "duration": 23}}, {"uid": "1900ca2a70f86979", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750404840233, "stop": 1750404840258, "duration": 25}}, {"uid": "7797747a8a5c8b41", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750404757189, "stop": 1750404757216, "duration": 27}}, {"uid": "5d138174970eb642", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'containers'", "time": {"start": 1750404502727, "stop": 1750404502758, "duration": 31}}], "categories": [{"name": "Test defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "e95b4258278bc102.json", "parameterValues": []}