{"uid": "525cb2afcb653ce3", "name": "Test build_entry_details function", "fullName": "test_containers_comprehensive.TestUtilityFunctions#test_build_entry_details", "historyId": "fbd3e49a19ffd4de90dba2135bb79da0", "time": {"start": 1750405275519, "stop": 1750405275520, "duration": 1}, "description": "Test build_entry_details function.", "descriptionHtml": "<p>Test build_entry_details function.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "<PERSON><PERSON> Manager", "time": {"start": 1750405275518, "stop": 1750405275518, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "3fb14793eef84a72", "status": "passed", "time": {"start": 1750405172017, "stop": 1750405172018, "duration": 1}}, {"uid": "6f9d514f54aae8b6", "status": "passed", "time": {"start": 1750404840624, "stop": 1750404840625, "duration": 1}}, {"uid": "e2713dd49eb58f", "status": "passed", "time": {"start": 1750404757599, "stop": 1750404757599, "duration": 0}}, {"uid": "f1594564d5fc4f46", "status": "passed", "time": {"start": 1750404503181, "stop": 1750404503182, "duration": 1}}], "categories": [], "tags": []}, "source": "525cb2afcb653ce3.json", "parameterValues": []}