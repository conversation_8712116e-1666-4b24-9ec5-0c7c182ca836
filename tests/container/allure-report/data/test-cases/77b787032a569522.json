{"uid": "77b787032a569522", "name": "Test PostgresSessionManager initialization", "fullName": "test_containers_comprehensive.TestPostgresSessionManager#test_postgres_session_manager_init", "historyId": "58915c616031366cca741bfd047864a7", "time": {"start": 1750404839694, "stop": 1750404839700, "duration": 6}, "description": "Test PostgresSessionManager initialization.", "descriptionHtml": "<p>Test PostgresSessionManager initialization.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404839563, "stop": 1750404839673, "duration": 110}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404839674, "stop": 1750404839674, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750404839694, "stop": 1750404839694, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Database Session Management"}, {"name": "feature", "value": "PostgresSessionManager"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "25276-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "77b787032a569522.json", "parameterValues": []}