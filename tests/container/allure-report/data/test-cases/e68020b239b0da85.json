{"uid": "e68020b239b0da85", "name": "Test EntryDetails string representation", "fullName": "test_containers_comprehensive.TestEntryDetails#test_entry_details_repr", "historyId": "5aebb147873c0d7c9c59acb5342ef611", "time": {"start": 1750404502132, "stop": 1750404502132, "duration": 0}, "description": "Test string representation hides password.", "descriptionHtml": "<p>Test string representation hides password.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404502005, "stop": 1750404502111, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404502112, "stop": 1750404502112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "EntryDetails"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "29004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "e68020b239b0da85.json", "parameterValues": []}