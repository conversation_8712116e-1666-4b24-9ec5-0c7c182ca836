{"uid": "b78044c6b3bef003", "name": "Test EntryDetails creation", "fullName": "test_containers_comprehensive.TestEntryDetails#test_entry_details_creation", "historyId": "711261e787fa8e9b43d1e5a6f6fbc321", "time": {"start": 1750405274524, "stop": 1750405274524, "duration": 0}, "description": "Test creating EntryDetails with all fields.", "descriptionHtml": "<p>Test creating EntryDetails with all fields.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "d7ccbabbd5403e0a", "status": "passed", "time": {"start": 1750405171067, "stop": 1750405171067, "duration": 0}}, {"uid": "710fd827c24b395f", "status": "passed", "time": {"start": 1750404839676, "stop": 1750404839677, "duration": 1}}, {"uid": "f73c9b60f438d145", "status": "passed", "time": {"start": 1750404756607, "stop": 1750404756607, "duration": 0}}, {"uid": "fef9334c75522178", "status": "passed", "time": {"start": 1750404502117, "stop": 1750404502117, "duration": 0}}], "categories": [], "tags": []}, "source": "b78044c6b3bef003.json", "parameterValues": []}