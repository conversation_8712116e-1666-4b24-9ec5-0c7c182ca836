{"uid": "898dfd7395b3e0d4", "name": "Test PostgresSessionManager initialization", "fullName": "test_containers_comprehensive.TestPostgresSessionManager#test_postgres_session_manager_init", "historyId": "58915c616031366cca741bfd047864a7", "time": {"start": 1750405171090, "stop": 1750405171100, "duration": 10}, "description": "Test PostgresSessionManager initialization.", "descriptionHtml": "<p>Test PostgresSessionManager initialization.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405170944, "stop": 1750405171064, "duration": 120}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405171064, "stop": 1750405171065, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405171089, "stop": 1750405171089, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Database Session Management"}, {"name": "feature", "value": "PostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28844-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "898dfd7395b3e0d4.json", "parameterValues": []}