{"uid": "966775e300a39d4e", "name": "Test PostgresSessionManager initialization", "fullName": "test_containers_comprehensive.TestPostgresSessionManager#test_postgres_session_manager_init", "historyId": "58915c616031366cca741bfd047864a7", "time": {"start": 1750405274544, "stop": 1750405274551, "duration": 7}, "description": "Test PostgresSessionManager initialization.", "descriptionHtml": "<p>Test PostgresSessionManager initialization.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405274543, "stop": 1750405274543, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "PostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Database Session Management"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "898dfd7395b3e0d4", "status": "passed", "time": {"start": 1750405171090, "stop": 1750405171100, "duration": 10}}, {"uid": "77b787032a569522", "status": "passed", "time": {"start": 1750404839694, "stop": 1750404839700, "duration": 6}}, {"uid": "8e527df3a2cd2db3", "status": "passed", "time": {"start": 1750404756625, "stop": 1750404756632, "duration": 7}}, {"uid": "df1b1f3aed62de27", "status": "passed", "time": {"start": 1750404502137, "stop": 1750404502142, "duration": 5}}], "categories": [], "tags": []}, "source": "966775e300a39d4e.json", "parameterValues": []}