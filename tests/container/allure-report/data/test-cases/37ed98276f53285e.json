{"uid": "37ed98276f53285e", "name": "Test schema override", "fullName": "test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_schema_override", "historyId": "74d15c067e1f91dd97d2bf53aef16d35", "time": {"start": 1750404502852, "stop": 1750404502872, "duration": 20}, "description": "Test schema override functionality.", "descriptionHtml": "<p>Test schema override functionality.</p>\n", "status": "broken", "statusMessage": "ModuleNotFoundError: No module named 'containers'", "statusTrace": "test_containers_comprehensive.py:304: in test_schema_override\n    container = DatabaseSessionManagerContainer()\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsrc/dependency_injector/containers.pyx:735: in dependency_injector.containers.DeclarativeContainer.__new__\n    ???\nsrc/dependency_injector/containers.pyx:287: in dependency_injector.containers.DynamicContainer.wire\n    ???\nsrc/dependency_injector/containers.pyx:903: in dependency_injector.containers._resolve_string_imports\n    ???\nC:\\Program Files\\Python312\\Lib\\importlib\\__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1324: in _find_and_load_unlocked\n    ???\nE   ModuleNotFoundError: No module named 'containers'", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750404502005, "stop": 1750404502111, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750404502112, "stop": 1750404502112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "29004-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "37ed98276f53285e.json", "parameterValues": []}