{"uid": "1abd4ec7fad4ee5", "name": "Test lifecycle manager cleanup", "fullName": "test_containers_comprehensive.TestManagedPostgresSessionManager#test_lifecycle_manager_cleanup", "historyId": "9181c6e5277604e01e981a4ec70d70b1", "time": {"start": 1750405275063, "stop": 1750405275064, "duration": 1}, "description": "Test lifecycle manager cleanup functionality.", "descriptionHtml": "<p>Test lifecycle manager cleanup functionality.</p>\n", "status": "failed", "statusMessage": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "statusTrace": "async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "Mock Database Entry Details", "time": {"start": 1750405275063, "stop": 1750405275063, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Managed Database Sessions"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "b2fd164ffbe3dabe", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750405171615, "stop": 1750405171616, "duration": 1}}, {"uid": "5ef8db6a25625011", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750404840218, "stop": 1750404840219, "duration": 1}}, {"uid": "205152b5e1cdbe92", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750404757166, "stop": 1750404757168, "duration": 2}}, {"uid": "d1ab940467585bb", "status": "failed", "statusDetails": "Failed: async def functions are not natively supported.\nYou need to install a suitable plugin for your async framework, for example:\n  - anyio\n  - pytest-asyncio\n  - pytest-tornasync\n  - pytest-trio\n  - pytest-twisted", "time": {"start": 1750404502707, "stop": 1750404502708, "duration": 1}}], "categories": [{"name": "Product defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "1abd4ec7fad4ee5.json", "parameterValues": []}