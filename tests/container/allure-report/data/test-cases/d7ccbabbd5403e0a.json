{"uid": "d7ccbabbd5403e0a", "name": "Test EntryDetails creation", "fullName": "test_containers_comprehensive.TestEntryDetails#test_entry_details_creation", "historyId": "711261e787fa8e9b43d1e5a6f6fbc321", "time": {"start": 1750405171067, "stop": 1750405171067, "duration": 0}, "description": "Test creating EntryDetails with all fields.", "descriptionHtml": "<p>Test creating EntryDetails with all fields.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405170944, "stop": 1750405171064, "duration": 120}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405171064, "stop": 1750405171065, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28844-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "d7ccbabbd5403e0a.json", "parameterValues": []}