{"uid": "46c379376aea2dde", "name": "Test lifecycle manager provider", "fullName": "test_containers_comprehensive.TestApplicationContainer#test_lifecycle_manager_provider", "historyId": "1a105423e670b2b2539ef46cf94f0c9a", "time": {"start": 1750405275439, "stop": 1750405275463, "duration": 24}, "description": "Test lifecycle manager provider.", "descriptionHtml": "<p>Test lifecycle manager provider.</p>\n", "status": "broken", "statusMessage": "ModuleNotFoundError: No module named 'utility_code'", "statusTrace": "test_containers_comprehensive.py:362: in test_lifecycle_manager_provider\n    container = ApplicationContainer()\n                ^^^^^^^^^^^^^^^^^^^^^^\nsrc/dependency_injector/containers.pyx:735: in dependency_injector.containers.DeclarativeContainer.__new__\n    ???\nsrc/dependency_injector/containers.pyx:287: in dependency_injector.containers.DynamicContainer.wire\n    ???\nsrc/dependency_injector/containers.pyx:903: in dependency_injector.containers._resolve_string_imports\n    ???\nC:\\Program Files\\Python312\\Lib\\importlib\\__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1324: in _find_and_load_unlocked\n    ???\nE   ModuleNotFoundError: No module named 'utility_code'", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "a0da9e1e0d2fd204", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750405171940, "stop": 1750405171961, "duration": 21}}, {"uid": "bff369c4a7c14380", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750404840551, "stop": 1750404840575, "duration": 24}}, {"uid": "eb542df05dbb7b03", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750404757513, "stop": 1750404757539, "duration": 26}}, {"uid": "1911bcae2d5ccfd4", "status": "broken", "statusDetails": "ModuleNotFoundError: No module named 'utility_code'", "time": {"start": 1750404503104, "stop": 1750404503125, "duration": 21}}], "categories": [{"name": "Test defects", "matchedStatuses": [], "flaky": false}], "tags": []}, "source": "46c379376aea2dde.json", "parameterValues": []}