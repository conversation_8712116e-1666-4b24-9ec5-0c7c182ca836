{"uid": "666a7450e6a06ab8", "name": "Test with_database_cleanup decorator for sync functions", "fullName": "test_containers_comprehensive.TestUtilityFunctions#test_with_database_cleanup_decorator_sync", "historyId": "4e34b51e9dba8f279f78022b79d49834", "time": {"start": 1750405275572, "stop": 1750405275573, "duration": 1}, "description": "Test with_database_cleanup decorator for synchronous functions.", "descriptionHtml": "<p>Test with_database_cleanup decorator for synchronous functions.</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 4, "retriesStatusChange": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1750405274381, "stop": 1750405274520, "duration": 139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}, {"name": "event_loop_policy", "time": {"start": 1750405274521, "stop": 1750405274521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false, "stepsCount": 0}], "afterStages": [], "labels": [{"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "severity", "value": "normal"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "test_containers_comprehensive"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "579832a6738c5233", "status": "passed", "time": {"start": 1750405172063, "stop": 1750405172065, "duration": 2}}, {"uid": "ef64dd7247cc5730", "status": "passed", "time": {"start": 1750404840664, "stop": 1750404840666, "duration": 2}}, {"uid": "e9540e792f031536", "status": "passed", "time": {"start": 1750404757643, "stop": 1750404757644, "duration": 1}}, {"uid": "9fef5dd5f331bf3c", "status": "passed", "time": {"start": 1750404503227, "stop": 1750404503227, "duration": 0}}], "categories": [], "tags": []}, "source": "666a7450e6a06ab8.json", "parameterValues": []}