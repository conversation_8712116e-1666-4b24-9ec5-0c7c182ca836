{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "test_containers_comprehensive", "children": [{"name": "TestEntryDetails", "children": [{"name": "Test EntryDetails creation", "uid": "b78044c6b3bef003", "parentUid": "2e47f81fd0f71e4a11e90b551638537c", "status": "passed", "time": {"start": 1750405274524, "stop": 1750405274524, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetailsBuilder pattern", "uid": "8b77d23f62e23ce1", "parentUid": "2e47f81fd0f71e4a11e90b551638537c", "status": "passed", "time": {"start": 1750405274532, "stop": 1750405274532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test EntryDetails string representation", "uid": "be18eef980fb8205", "parentUid": "2e47f81fd0f71e4a11e90b551638537c", "status": "passed", "time": {"start": 1750405274538, "stop": 1750405274539, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "2e47f81fd0f71e4a11e90b551638537c"}, {"name": "TestPostgresSessionManager", "children": [{"name": "Test PostgresSessionManager initialization", "uid": "966775e300a39d4e", "parentUid": "9410f771e62a544020770b97ee3cc156", "status": "passed", "time": {"start": 1750405274544, "stop": 1750405274551, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema update functionality", "uid": "26e67861079dc1ed", "parentUid": "9410f771e62a544020770b97ee3cc156", "status": "failed", "time": {"start": 1750405274559, "stop": 1750405274562, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "9410f771e62a544020770b97ee3cc156"}, {"name": "TestManagedPostgresSessionManager", "children": [{"name": "Test auto-registration with lifecycle manager", "uid": "798cbcdd93ddb460", "parentUid": "12b4ffa9b50ff103051199b1ce3d71b1", "status": "passed", "time": {"start": 1750405275054, "stop": 1750405275056, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager cleanup", "uid": "1abd4ec7fad4ee5", "parentUid": "12b4ffa9b50ff103051199b1ce3d71b1", "status": "failed", "time": {"start": 1750405275063, "stop": 1750405275064, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "12b4ffa9b50ff103051199b1ce3d71b1"}, {"name": "TestDatabaseSessionManagerContainer", "children": [{"name": "Test container initialization", "uid": "e95b4258278bc102", "parentUid": "365c8f8722f5286d104cc292370635fa", "status": "broken", "time": {"start": 1750405275082, "stop": 1750405275106, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test schema override", "uid": "7849036c2f78ddaa", "parentUid": "365c8f8722f5286d104cc292370635fa", "status": "broken", "time": {"start": 1750405275186, "stop": 1750405275210, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database provider resolution", "uid": "d1c2d194120b79a2", "parentUid": "365c8f8722f5286d104cc292370635fa", "status": "broken", "time": {"start": 1750405275270, "stop": 1750405275303, "duration": 33}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "365c8f8722f5286d104cc292370635fa"}, {"name": "TestApplicationContainer", "children": [{"name": "Test ApplicationContainer initialization", "uid": "1e7407e2f66e9c83", "parentUid": "a88cd794e431243fb40a90ae9eb0da7a", "status": "broken", "time": {"start": 1750405275356, "stop": 1750405275386, "duration": 30}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test lifecycle manager provider", "uid": "46c379376aea2dde", "parentUid": "a88cd794e431243fb40a90ae9eb0da7a", "status": "broken", "time": {"start": 1750405275439, "stop": 1750405275463, "duration": 24}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "a88cd794e431243fb40a90ae9eb0da7a"}, {"name": "TestUtilityFunctions", "children": [{"name": "Test build_entry_details function", "uid": "525cb2afcb653ce3", "parentUid": "10911c79a86acbc4b5225d6f7ba9a8ed", "status": "passed", "time": {"start": 1750405275519, "stop": 1750405275520, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test database_lifecycle context manager", "uid": "f8d92e6405a94177", "parentUid": "10911c79a86acbc4b5225d6f7ba9a8ed", "status": "failed", "time": {"start": 1750405275528, "stop": 1750405275528, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator", "uid": "decf358f50812dbe", "parentUid": "10911c79a86acbc4b5225d6f7ba9a8ed", "status": "failed", "time": {"start": 1750405275554, "stop": 1750405275554, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test with_database_cleanup decorator for sync functions", "uid": "666a7450e6a06ab8", "parentUid": "10911c79a86acbc4b5225d6f7ba9a8ed", "status": "passed", "time": {"start": 1750405275572, "stop": 1750405275573, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "10911c79a86acbc4b5225d6f7ba9a8ed"}, {"name": "TestError<PERSON><PERSON>ling", "children": [{"name": "Test build_entry_details with missing entry", "uid": "c19986838582c641", "parentUid": "577235a30b37a3e74740afd6a8b7d59b", "status": "failed", "time": {"start": 1750405275580, "stop": 1750405275584, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}, {"name": "Test PostgresSessionManager operations after close", "uid": "73a6856ba89ee634", "parentUid": "577235a30b37a3e74740afd6a8b7d59b", "status": "passed", "time": {"start": 1750405275608, "stop": 1750405275617, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 4, "retriesStatusChange": false, "parameters": [], "tags": []}], "uid": "577235a30b37a3e74740afd6a8b7d59b"}], "uid": "a40e099ae430baf838c33d0bd88179a0"}]}