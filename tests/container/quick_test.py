#!/usr/bin/env python3
"""
Quick test script to verify the environment is working.

This script runs a minimal test to check if the database drivers
and basic functionality are working correctly.
"""

import sys
import subprocess
from pathlib import Path


def test_imports():
    """Test basic imports."""
    print("🔍 Testing basic imports...")
    
    try:
        # Test SQLAlchemy
        from sqlalchemy import create_engine
        from sqlalchemy.engine import URL
        print("✅ SQLAlchemy imports successful")
        
        # Test database drivers
        import psycopg2
        print(f"✅ psycopg2: {psycopg2.__version__}")
        
        import asyncpg
        print(f"✅ asyncpg: {asyncpg.__version__}")
        
        # Test dependency injection
        from dependency_injector import containers, providers
        print("✅ dependency-injector imports successful")
        
        # Test pytest
        import pytest
        print(f"✅ pytest: {pytest.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_url_creation():
    """Test SQLAlchemy URL creation."""
    print("\n🔍 Testing SQLAlchemy URL creation...")
    
    try:
        from sqlalchemy.engine import URL
        
        # Test psycopg2 URL
        sync_url = URL.create(
            drivername="postgresql+psycopg2",
            username="test",
            password="test",
            host="localhost",
            database="test"
        )
        print(f"✅ psycopg2 URL: {sync_url}")
        
        # Test asyncpg URL
        async_url = URL.create(
            drivername="postgresql+asyncpg",
            username="test",
            password="test",
            host="localhost",
            database="test"
        )
        print(f"✅ asyncpg URL: {async_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL creation error: {e}")
        return False


def test_psycopg3_conflict():
    """Test if psycopg3 is installed (should not be)."""
    print("\n🔍 Checking for psycopg3 conflicts...")
    
    try:
        import psycopg
        print(f"⚠️  psycopg3 is installed: {psycopg.__version__}")
        print("   This may cause conflicts with psycopg2")
        return False
    except ImportError:
        print("✅ psycopg3 is not installed (good)")
        return True


def run_basic_pytest():
    """Run a basic pytest command."""
    print("\n🧪 Running basic pytest...")
    
    # Create a simple test file
    test_content = '''
import pytest

def test_simple():
    """Simple test to verify pytest is working."""
    assert 1 + 1 == 2

@pytest.mark.asyncio
async def test_async_simple():
    """Simple async test."""
    import asyncio
    await asyncio.sleep(0.001)
    assert True
'''
    
    test_file = Path(__file__).parent / "temp_test.py"
    try:
        test_file.write_text(test_content)
        
        # Try to run with uv first, then fallback to python
        commands_to_try = [
            ["uv", "run", "pytest", str(test_file), "-v"],
            [sys.executable, "-m", "pytest", str(test_file), "-v"]
        ]
        
        for cmd in commands_to_try:
            try:
                result = subprocess.run(cmd, check=True, capture_output=True, text=True)
                print(f"✅ pytest successful with: {' '.join(cmd[:2])}")
                print(result.stdout)
                return True
            except (subprocess.CalledProcessError, FileNotFoundError) as e:
                print(f"❌ Failed with {' '.join(cmd[:2])}: {e}")
                continue
        
        return False
        
    finally:
        # Clean up temp file
        if test_file.exists():
            test_file.unlink()


def main():
    """Main function."""
    print("🚀 QUICK ENVIRONMENT TEST")
    print("=" * 50)
    
    all_tests = [
        ("Basic imports", test_imports),
        ("URL creation", test_url_creation),
        ("psycopg3 conflicts", test_psycopg3_conflict),
        ("Basic pytest", run_basic_pytest),
    ]
    
    results = []
    for test_name, test_func in all_tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Environment is ready.")
        print("\nNext steps:")
        print("  1. Run: python test_basic_setup.py")
        print("  2. Run: python run_tests.py --coverage --allure")
        return 0
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")
        print("\nTroubleshooting:")
        print("  1. Check: python check_environment.py")
        print("  2. Setup: python setup_uv_env.py")
        print("  3. Install: python run_tests.py --install-deps --legacy-drivers")
        return 1


if __name__ == "__main__":
    sys.exit(main())
