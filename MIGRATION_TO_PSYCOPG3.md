# Migration to psycopg3 (PostgreSQL Driver Unification)

This document outlines the migration from using separate drivers (psycopg2 for sync, asyncpg for async) to using psycopg3 for both synchronous and asynchronous database operations.

## Overview

### Before Migration
- **Sync operations**: SQLAlchemy + psycopg2 (`postgresql+psycopg2`)
- **Async operations**: SQLAlchemy + asyncpg (`postgresql+asyncpg`)
- **Issues**: Different connection pooling behaviors, potential resource contention, blocking with large datasets

### After Migration
- **Both sync and async**: SQLAlchemy + psycopg3 (`postgresql+psycopg`)
- **Benefits**: Unified driver, consistent connection pooling, better performance with large datasets, enhanced monitoring

## Changes Made

### 1. Dependencies Updated

**pyproject.toml changes:**
```toml
# Removed
"asyncpg",
"psycopg2-binary",

# Added
"psycopg[binary]",  # Modern PostgreSQL adapter for both sync and async
```

### 2. Database Connection Strings Updated

**containers.py changes:**
- Sync engine: `postgresql+psycopg2` → `postgresql+psycopg`
- Async engine: `postgresql+asyncpg` → `postgresql+psycopg`

### 3. Enhanced Connection Configuration

**New psycopg3-specific settings:**
```python
connect_args={
    "server_settings": {
        "application_name": f"airflow_app_{self.schema}",
        "tcp_keepalives_idle": "600",
        "tcp_keepalives_interval": "30",
        "tcp_keepalives_count": "3",
    },
    "autocommit": False,
    "prepare_threshold": 5,  # Enable prepared statements
}
```

### 4. Improved Connection Pool Settings

- **Sync pool**: size=20, max_overflow=15, timeout=45s
- **Async pool**: size=15, max_overflow=25, timeout=45s
- **Both**: pool_recycle=3600s, pool_pre_ping=True

## New Debugging and Monitoring Tools

### 1. Database Monitor (`database_monitor.py`)

**Features:**
- Real-time query tracking
- Memory usage monitoring
- Connection pool statistics
- Long-running query detection

**Usage:**
```python
from dags.data_pipeline.utilities.database_monitor import get_database_monitor

monitor = get_database_monitor()

# Manual tracking
with monitor.track_query("my_query"):
    # Your database operation

# Async tracking
async with monitor.track_async_query("my_async_query"):
    # Your async database operation

# Start monitoring dashboard
await monitor.start_monitoring(display_live=True)
```

### 2. Blocking Detector (`blocking_detector.py`)

**Features:**
- Detects operations running longer than threshold (default: 30s)
- Stack trace capture for debugging
- Emergency debugging functions
- Rich console reporting

**Usage:**
```python
from dags.data_pipeline.utilities.blocking_detector import detect_blocking, get_blocking_detector

# Decorator usage
@detect_blocking("my_operation")
def my_function():
    # Your code here
    pass

# Manual tracking
detector = get_blocking_detector()
with detector.track_operation("operation_name"):
    # Your code here
    pass

# Emergency debugging
from dags.data_pipeline.utilities.blocking_detector import emergency_stack_dump
emergency_stack_dump()  # Call when code appears stuck
```

### 3. Enhanced Database Operations (`enhanced_database_ops.py`)

**Features:**
- Adaptive batch sizing based on memory usage
- Progress tracking for large operations
- Streaming capabilities for large result sets
- Automatic monitoring integration

**Usage:**
```python
from dags.data_pipeline.utilities.enhanced_database_ops import enhanced_upsert_async

# Enhanced upsert with monitoring
stats = await enhanced_upsert_async(
    session=session,
    model=MyModel,
    rows=large_dataframe,
    max_memory_mb=500.0,
    progress_callback=my_progress_callback
)
```

## Migration Steps

### 1. Install New Dependencies

```bash
# Using uv (recommended)
uv sync --group modern

# Or using pip
pip install "psycopg[binary]"
pip uninstall psycopg2-binary asyncpg
```

### 2. Test the Migration

```bash
python scripts/migrate_to_psycopg3.py
```

This script will test:
- Basic connectivity (sync and async)
- Connection pooling behavior
- Large data operations
- Monitoring and debugging tools

### 3. Monitor Performance

After migration, monitor your application for:
- **Connection pool exhaustion**: Check logs for pool timeout errors
- **Memory usage**: Monitor memory consumption during large operations
- **Query performance**: Use the database monitor to track slow queries
- **Blocking operations**: Use the blocking detector to identify stuck operations

## Debugging When Code Gets Stuck

### 1. Real-time Monitoring

Start the monitoring dashboard:
```python
import asyncio
from dags.data_pipeline.utilities.database_monitor import get_database_monitor
from dags.data_pipeline.utilities.blocking_detector import get_blocking_detector

async def start_monitoring():
    monitor = get_database_monitor()
    detector = get_blocking_detector()
    
    # Start both monitors
    await asyncio.gather(
        monitor.start_monitoring(display_live=True),
        detector.start_monitoring(display_live=True)
    )

asyncio.run(start_monitoring())
```

### 2. Emergency Debugging

If your code gets stuck, you can:

**Option 1: Call emergency functions**
```python
from dags.data_pipeline.utilities.blocking_detector import emergency_stack_dump, emergency_blocking_report

emergency_stack_dump()        # Shows all thread stack traces
emergency_blocking_report()   # Shows blocking operations
```

**Option 2: Send signals (Linux/Mac)**
```bash
# Get process ID
ps aux | grep python

# Send signals
kill -USR1 <pid>  # Dump all thread stacks
kill -USR2 <pid>  # Show blocking operations
```

### 3. Analyze Logs

The enhanced logging will show:
- Long-running queries (>30s by default)
- Memory usage spikes
- Connection pool statistics
- Batch processing progress

## Performance Optimizations

### 1. For Large Datasets (50,000+ records)

```python
# Use enhanced upsert with adaptive batching
await enhanced_upsert_async(
    session=session,
    model=MyModel,
    rows=large_df,
    max_memory_mb=500.0,  # Adjust based on available memory
    batch_size=1000,      # Starting batch size
    yield_control_every=5  # Yield to event loop every 5 batches
)
```

### 2. For Streaming Large Results

```python
# Stream large query results
async for chunk_df in stream_large_query_async(
    session=session,
    query="SELECT * FROM large_table WHERE condition = :param",
    parameters={"param": "value"},
    chunk_size=10000
):
    # Process each chunk
    process_chunk(chunk_df)
```

### 3. Connection Pool Tuning

Adjust pool settings based on your workload:
```python
# For high-concurrency workloads
pool_size=25
max_overflow=35

# For memory-constrained environments
pool_size=10
max_overflow=15
```

## Rollback Plan

If issues arise, you can rollback by:

1. **Revert dependencies:**
```bash
uv sync --group legacy  # Uses psycopg2-binary
```

2. **Revert connection strings:**
```python
# In containers.py
drivername="postgresql+psycopg2"  # For sync
drivername="postgresql+asyncpg"   # For async
```

3. **Remove monitoring code** (optional, but monitoring works with old drivers too)

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure psycopg3 is installed: `pip show psycopg`
2. **Connection timeouts**: Increase `pool_timeout` in engine configuration
3. **Memory issues**: Reduce `max_memory_mb` in enhanced operations
4. **Performance regression**: Check connection pool settings and batch sizes

### Getting Help

1. **Check logs**: Enhanced logging provides detailed information
2. **Use monitoring tools**: Database monitor and blocking detector
3. **Run migration test**: `python scripts/migrate_to_psycopg3.py`
4. **Emergency debugging**: Use emergency functions when stuck

## Benefits Achieved

After successful migration:

1. **Unified driver**: Single driver for both sync and async operations
2. **Better performance**: Optimized connection pooling and prepared statements
3. **Enhanced monitoring**: Real-time visibility into database operations
4. **Debugging capabilities**: Tools to identify and resolve blocking issues
5. **Adaptive processing**: Automatic batch size adjustment for large datasets
6. **Memory management**: Monitoring and control of memory usage

The migration provides a more robust, monitorable, and debuggable database layer that should resolve the blocking issues with large datasets while providing better visibility into system behavior.
