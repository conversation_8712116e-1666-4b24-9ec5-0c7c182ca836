[project]
name = "airflow"
dynamic = ["version"]
description = "JIRA data pipeline for extracting and storing data from Jira Cloud to PostgreSQL"
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "vishal<PERSON><PERSON><EMAIL>"}
]
dependencies = [
    "aiohttp",
    "asyncpg",
    "babel",
    "beautifulsoup4",
    "dependency-injector",
    "hypothesis",
    "hatchling",
    "hatch-vcs",
    "markdownify",
    "num2words",
    "pandas",
    "passlib",
    "pykeepass",
    "psycopg2-binary",
    "pydantic",
    "PyYAML",
    "requests",
    "rich",
    "sqlalchemy",
    "sqlalchemy_utils",
    "sqlalchemy_json",
    "sqlalchemy-citext",
    "xlsxwriter"
]

[project.optional-dependencies]
dev = [

    "black",
    "isort",
    "mypy",
    "types-SQLAlchemy",

]
linux = [
    "apache-airflow>=3.0.0"
]
docs = [
    "sphinx",
    "sphinx_autodoc_typehints",
    "sphinx_copybutton",
    "myst_parser[linkify]",
    "nbsphinx",
    "sphinx-rtd-theme",
    "sphinx-autoapi",
]



[dependency-groups]
# Legacy database drivers (psycopg2) - recommended for existing codebases
legacy = [
    "psycopg2-binary",  # PostgreSQL adapter for Python (sync)
]

# Modern database drivers (psycopg3) - for new projects
modern = [
    "psycopg[binary]",  # Modern PostgreSQL adapter
]
# Test dependencies group
test = [
    # Core testing framework
    "pytest",
    "pytest-asyncio",
    "pytest-mock",
    "pytest-dotenv",
    # Coverage reporting
    "pytest-cov",
    "coverage[toml]",
    # Allure reporting
    "allure-pytest",
    "allure-python-commons",
    "pytest-allure-spec-coverage",
    # Additional testing utilities
    "pytest-xdist",  # Parallel test execution
    "pytest-timeout",  # Test timeouts
    "pytest-benchmark",  # Performance benchmarking
    "pytest-html",  # HTML test reports
    # Mocking and fixtures
    "responses",  # HTTP mocking
    "freezegun",  # Time mocking
    "factory-boy",  # Test data factories

    # Database testing utilities
    "pytest-postgresql==4.1.1",  # PostgreSQL testing. This is the last version that supports pyscopg2
    "sqlalchemy-utils",  # Database utilities

    # Async testing utilities
    "aioresponses",  # Async HTTP mocking
    "pytest-aiohttp",  # aiohttp testing
    # Additional test-only dependencies can go here
    "pytest-sugar",  # Better test output
    "pytest-clarity",  # Better assertion output
]

[build-system]
requires = ["hatchling", "hatch-vcs"]
build-backend = "hatchling.build"


[tool.uv]
required-version = "0.7.3"

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false


[tool.black]
line-length = 100
target-version = ["py310", 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
known_first_party = ["dags"]

[tool.pytest.ini_options]
# Pytest configuration
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]



# Markers
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "database: Tests requiring database connection",
    "async: Asynchronous tests",
]

# Coverage settings
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short"
]

# Async test configuration
asyncio_mode = "auto"

# Logging
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# Warnings
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning:sqlalchemy.*",
]

# Minimum version
minversion = "6.0"

[tool.hatch.version]
source = "vcs"

[tool.hatch.build.targets.wheel]
packages = ["dags"]

[tool.coverage.run]
source = ["dags.data_pipeline.containers"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]