#!/usr/bin/env python3
# coding=utf-8
"""
Basic test script to verify psycopg3 installation and functionality.
"""
import sys
import asyncio
from pathlib import Path

def test_psycopg3_import():
    """Test if psycopg3 can be imported."""
    try:
        import psycopg
        print(f"✓ psycopg3 imported successfully - version: {psycopg.__version__}")
        return True
    except ImportError as e:
        print(f"✗ Failed to import psycopg3: {e}")
        return False

def test_sqlalchemy_psycopg():
    """Test SQLAlchemy with psycopg3."""
    try:
        from sqlalchemy import create_engine, text
        from sqlalchemy.engine import URL
        
        # Test URL creation
        url = URL.create(
            drivername="postgresql+psycopg",
            username="test",
            password="test",
            host="localhost",
            port=5432,
            database="test"
        )
        print(f"✓ SQLAlchemy URL created: {url}")
        
        # Test engine creation (won't connect, just create)
        engine = create_engine(url, strategy='mock', executor=lambda sql, *_: None)
        print("✓ SQLAlchemy engine created with psycopg3 driver")
        return True
        
    except Exception as e:
        print(f"✗ SQLAlchemy psycopg3 test failed: {e}")
        return False

async def test_async_sqlalchemy_psycopg():
    """Test async SQLAlchemy with psycopg3."""
    try:
        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy.engine import URL
        
        # Test async URL creation
        url = URL.create(
            drivername="postgresql+psycopg",
            username="test",
            password="test",
            host="localhost",
            port=5432,
            database="test"
        )
        
        # Test async engine creation (won't connect, just create)
        engine = create_async_engine(url, strategy='mock')
        print("✓ Async SQLAlchemy engine created with psycopg3 driver")
        return True
        
    except Exception as e:
        print(f"✗ Async SQLAlchemy psycopg3 test failed: {e}")
        return False

def test_monitoring_imports():
    """Test if our new monitoring utilities can be imported."""
    try:
        # Add project root to path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from dags.data_pipeline.utilities.database_monitor import DatabaseMonitor
        from dags.data_pipeline.utilities.blocking_detector import BlockingDetector
        from dags.data_pipeline.utilities.enhanced_database_ops import EnhancedChunker
        
        print("✓ All monitoring utilities imported successfully")
        
        # Test basic functionality
        monitor = DatabaseMonitor()
        detector = BlockingDetector()
        chunker = EnhancedChunker()
        
        print("✓ Monitoring utilities instantiated successfully")
        return True
        
    except Exception as e:
        print(f"✗ Monitoring utilities test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("=" * 60)
    print("psycopg3 Migration Basic Tests")
    print("=" * 60)
    
    tests = [
        ("psycopg3 Import", test_psycopg3_import),
        ("SQLAlchemy + psycopg3", test_sqlalchemy_psycopg),
        ("Async SQLAlchemy + psycopg3", test_async_sqlalchemy_psycopg),
        ("Monitoring Utilities", test_monitoring_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All basic tests passed! psycopg3 migration appears successful.")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Check the output above.")

if __name__ == "__main__":
    # Set up event loop policy for Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())
